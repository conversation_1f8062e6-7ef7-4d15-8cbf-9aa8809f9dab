/**
 * Advanced UI/UX and Microinteractions for Odoo-style AgnoShin Website
 */

// Fade-in animation for elements on scroll
document.addEventListener('DOMContentLoaded', function() {
  // Create intersection observer for fade-in animations
  const fadeElements = document.querySelectorAll('.app-card, .section-title, .section-subtitle, .about-content, .product-content');

  const fadeObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('fade-in-visible');
        fadeObserver.unobserve(entry.target);
      }
    });
  }, {
    root: null,
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });

  fadeElements.forEach(element => {
    element.classList.add('fade-in');
    fadeObserver.observe(element);
  });

  // Enhanced hover effects for app cards
  const appCards = document.querySelectorAll('.app-card');

  appCards.forEach(card => {
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'app-tooltip';

    // Get service description based on title
    const title = card.querySelector('.app-title').textContent;
    let description = '';

    switch(title) {
      case 'Professional Services':
        description = 'Expert knowledge and tools customized for your contact center needs';
        break;
      case 'Managed Services':
        description = 'Cost-efficient approach to contact center management';
        break;
      case 'Big Data Analytics':
        description = 'Implement big data programs across multiple domains';
        break;
      case 'Cloud Services':
        description = 'Transform your business to the cloud with our advisory services';
        break;
      case 'Security Services':
        description = 'IT security solutions addressing key enterprise challenges';
        break;
      case 'Customer Experience':
        description = 'Develop strategies that take into account your unique business context';
        break;
      case 'AgnoCon':
        description = 'Intelligent customer experience management software';
        break;
      case 'AgnoVideo':
        description = 'Face-to-face video communication platform with AI capabilities';
        break;
      case 'Omni Channel':
        description = 'Seamless communication across multiple channels';
        break;
      case 'Campaign Management':
        description = 'Plan, execute and track marketing campaigns effectively';
        break;
      case 'Real-Time Dashboard':
        description = 'Monitor performance metrics in real-time';
        break;
      case 'Analytics':
        description = 'Gain insights from your data to make informed decisions';
        break;
      case 'AgKnow':
        description = 'AI-powered knowledge management system for efficient information organization';
        break;
      case 'Survey Assist':
        description = 'Interactive Voice Response survey solution for collecting valuable customer feedback';
        break;
      case 'Great Place to Work':
        description = 'Certified for creating a high-trust and high-performance culture';
        break;
      default:
        description = 'Click to learn more about our services';
    }

    tooltip.textContent = description;
    card.appendChild(tooltip);

    // Variables to track intentional hover/touch
    let isIntentionalInteraction = false;
    let hoverTimer = null;
    let tooltipVisible = false;

    // Enhanced hover interactions
    card.addEventListener('mouseenter', function() {
      // Scale up slightly but less dramatically
      this.style.transform = 'translateY(-5px) scale(1.01)';
      this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.08)';
      this.style.zIndex = '10';

      // Only show tooltip after a deliberate hover
      clearTimeout(hoverTimer);
      hoverTimer = setTimeout(() => {
        isIntentionalInteraction = true;
        if (!tooltipVisible) {
          tooltip.style.opacity = '1';
          tooltip.style.transform = 'translateY(0)';
          tooltipVisible = true;
        }
      }, 100); // Reduced delay for faster response

      // Subtle rotation of icon
      const icon = this.querySelector('.app-icon');
      icon.style.transform = 'scale(1.1)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
      this.style.boxShadow = 'var(--card-shadow)';
      this.style.zIndex = '1';

      // Clear hover timer
      clearTimeout(hoverTimer);
      isIntentionalInteraction = false;

      // Hide tooltip quickly
      tooltip.style.opacity = '0';
      tooltip.style.transform = 'translateY(10px)';
      tooltipVisible = false;

      // Reset icon
      const icon = this.querySelector('.app-icon');
      icon.style.transform = 'scale(1)';
    });

    // Touch events for mobile
    card.addEventListener('touchstart', function(e) {
      // Start timer to detect intentional touch
      clearTimeout(hoverTimer);
      hoverTimer = setTimeout(() => {
        isIntentionalInteraction = true;
      }, 50);
    });

    card.addEventListener('touchend', function(e) {
      // If it was an intentional touch, show tooltip briefly
      if (isIntentionalInteraction && !tooltipVisible) {
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'translateY(0)';
        tooltipVisible = true;

        // Hide tooltip after a short period
        setTimeout(() => {
          tooltip.style.opacity = '0';
          tooltip.style.transform = 'translateY(10px)';
          tooltipVisible = false;
        }, 1500); // Show for 1.5 seconds then hide
      }

      clearTimeout(hoverTimer);
      isIntentionalInteraction = false;
    });

    // Add click ripple effect
    card.addEventListener('click', function(e) {
      // Create ripple element
      const ripple = document.createElement('span');
      ripple.className = 'ripple-effect';
      this.appendChild(ripple);

      // Position the ripple
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${e.clientX - rect.left - size/2}px`;
      ripple.style.top = `${e.clientY - rect.top - size/2}px`;

      // Remove after animation completes
      setTimeout(() => {
        ripple.remove();
      }, 600);

      // Check if this is a service card with a link and navigate
      const link = this.querySelector('a[href]');
      console.log('Card clicked:', this);
      console.log('Link found:', link);

      if (link && link.href) {
        console.log('Navigating to:', link.href);
        // Prevent default behavior and navigate directly
        e.preventDefault();
        e.stopPropagation();

        // Small delay to show ripple effect before navigation
        setTimeout(() => {
          window.location.href = link.href;
        }, 150);
      } else {
        // Fallback: check if this card has a title and navigate based on that
        const title = this.querySelector('.app-title');
        if (title) {
          const titleText = title.textContent.trim();
          console.log('Title found:', titleText);

          let targetUrl = '';
          switch(titleText) {
            case 'Professional Services':
              targetUrl = 'services/professional.html';
              break;
            case 'Managed Services':
              targetUrl = 'services/managed.html';
              break;
            case 'Big Data Analytics':
              targetUrl = 'services/bigdata.html';
              break;
            case 'Cloud Services':
              targetUrl = 'services/cloud.html';
              break;
            case 'Security Services':
              targetUrl = 'services/security.html';
              break;
            case 'Customer Experience':
              targetUrl = 'services/customer-experience.html';
              break;
          }

          if (targetUrl) {
            console.log('Fallback navigation to:', targetUrl);
            setTimeout(() => {
              window.location.href = targetUrl;
            }, 150);
          }
        }
      }
    });
  });

  // Sticky header with scroll effect
  const header = document.querySelector('.header');
  let lastScrollTop = 0;

  window.addEventListener('scroll', function() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (scrollTop > 50) {
      header.classList.add('header-scrolled');
    } else {
      header.classList.remove('header-scrolled');
    }

    if (scrollTop > lastScrollTop && scrollTop > 200) {
      // Scrolling down
      header.style.transform = 'translateY(-100%)';
    } else {
      // Scrolling up
      header.style.transform = 'translateY(0)';
    }

    lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
  });

  // Button hover effects
  const buttons = document.querySelectorAll('.btn');

  buttons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
    });

    button.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });

    // Add click ripple effect
    button.addEventListener('click', function(e) {
      // Create ripple element
      const ripple = document.createElement('span');
      ripple.className = 'ripple-effect';
      this.appendChild(ripple);

      // Position the ripple
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${e.clientX - rect.left - size/2}px`;
      ripple.style.top = `${e.clientY - rect.top - size/2}px`;

      // Remove after animation completes
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Animated counter for stats (if added later)
  function animateCounter(element, target, duration) {
    let start = 0;
    const increment = target / (duration / 16);

    function updateCount() {
      start += increment;
      if (start >= target) {
        element.textContent = target;
        return;
      }

      element.textContent = Math.floor(start);
      requestAnimationFrame(updateCount);
    }

    updateCount();
  }

  // Initialize any counters if they exist
  const counters = document.querySelectorAll('.counter');
  if (counters.length > 0) {
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const target = parseInt(entry.target.getAttribute('data-target'));
          animateCounter(entry.target, target, 1500);
          counterObserver.unobserve(entry.target);
        }
      });
    });

    counters.forEach(counter => {
      counterObserver.observe(counter);
    });
  }

  // Smooth navigation highlighting
  const sections = document.querySelectorAll('section[id]');
  const navLinks = document.querySelectorAll('.nav-link');

  window.addEventListener('scroll', function() {
    let current = '';
    const scrollPosition = window.scrollY;

    sections.forEach(section => {
      const sectionTop = section.offsetTop - 100;
      const sectionHeight = section.offsetHeight;

      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        current = section.getAttribute('id');
      }
    });

    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === `#${current}`) {
        link.classList.add('active');
      }
    });
  });

  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const navMenu = document.querySelector('.nav-menu');

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener('click', function() {
      navMenu.classList.toggle('active');
      this.classList.toggle('active');
    });
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(e) {
    if (navMenu && navMenu.classList.contains('active')) {
      if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
        navMenu.classList.remove('active');
        mobileMenuToggle.classList.remove('active');
      }
    }
  });

  // Product card selection
  const productCards = document.querySelectorAll('.product-card');

  if (productCards.length > 0) {
    productCards.forEach(card => {
      card.addEventListener('click', function() {
        // Remove active class from all cards
        productCards.forEach(c => c.classList.remove('active'));

        // Add active class to clicked card
        this.classList.add('active');

        // Get product data
        const productType = this.getAttribute('data-product');

        // Navigate to the appropriate product page based on the product type
        if (productType === 'agnocon') {
          window.location.href = 'products/agnocon.html';
        } else if (productType === 'agnovideo') {
          window.location.href = 'products/agnovideo.html';
        } else if (productType === 'agknow') {
          window.location.href = 'products/agknow.html';
        } else if (productType === 'surveyassist') {
          window.location.href = 'products/surveyassist.html';
        } else if (productType === 'agentscripting') {
          window.location.href = 'products/agentscripting.html';
        } else if (productType === 'outbounddialer') {
          window.location.href = 'products/outbounddialer.html';
        } else if (productType === 'wallboard') {
          window.location.href = 'products/wallboard.html';
        } else {
          // For other products, we could add more product pages in the future
          console.log(`Selected product: ${productType}`);
          // For now, just show an alert
          alert(`Details for ${productType} coming soon!`);
        }
      });
    });
  }

  // Add parallax effect to hero section
  const hero = document.querySelector('.hero');
  if (hero) {
    window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      if (scrollPosition < 600) {
        hero.style.backgroundPositionY = `${scrollPosition * 0.5}px`;
      }
    });
  }
});
