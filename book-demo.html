<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Book AgnoCon Demo - AgnoShin Technologies</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="enhance_style.css">
  <link rel="stylesheet" href="enhanced_ui.css">

  <style>
    /* Demo Page Specific Styles */
    .demo-hero {
      background: linear-gradient(135deg, #073763 0%, #0B2A5A 50%, #F47C20 100%);
      color: white;
      padding: 80px 0;
      position: relative;
      overflow: hidden;
    }

    .demo-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('media/agnocon_main.jpeg') center/cover;
      opacity: 0.1;
      z-index: 1;
    }

    .demo-hero .container {
      position: relative;
      z-index: 2;
    }

    .demo-hero h1 {
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      color: white;
      text-align: center;
    }

    .demo-hero p {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      text-align: center;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      opacity: 0.95;
    }

    .demo-cta {
      text-align: center;
      margin-top: 2rem;
    }

    .demo-cta .btn {
      padding: 15px 30px;
      font-size: 1.1rem;
      border-radius: 50px;
      background: linear-gradient(45deg, #F47C20, #FF8C42);
      border: none;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(244, 124, 32, 0.3);
    }

    .demo-cta .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(244, 124, 32, 0.4);
    }

    /* Support Section */
    .support-section {
      padding: 80px 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .support-header {
      text-align: center;
      margin-bottom: 60px;
    }

    .support-header h2 {
      font-size: 2.5rem;
      color: #073763;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .support-header p {
      font-size: 1.2rem;
      color: #6E7C8E;
      max-width: 800px;
      margin: 0 auto;
    }

    .support-highlight {
      color: #F47C20;
      font-weight: 600;
    }

    .support-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 40px;
      margin-top: 60px;
    }

    .support-card {
      background: white;
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid rgba(244, 124, 32, 0.1);
    }

    .support-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .support-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(45deg, #F47C20, #FF8C42);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      color: white;
      font-size: 2rem;
    }

    .support-card h3 {
      font-size: 1.5rem;
      color: #073763;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .support-card p {
      color: #6E7C8E;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .contact-info {
      font-weight: 600;
      color: #F47C20;
    }

    .contact-info a {
      color: #F47C20;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .contact-info a:hover {
      color: #073763;
    }

    /* Demo Form Section */
    .demo-form-section {
      padding: 80px 0;
      background: #073763;
      color: white;
    }

    .form-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      padding: 50px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .form-header {
      text-align: center;
      margin-bottom: 40px;
    }

    .form-header h2 {
      font-size: 2.5rem;
      color: #073763;
      margin-bottom: 1rem;
      font-weight: 700;
    }

    .form-header p {
      color: #6E7C8E;
      font-size: 1.1rem;
    }

    .demo-form {
      display: grid;
      gap: 25px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      color: #073763;
      margin-bottom: 8px;
      font-size: 0.95rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 15px;
      border: 2px solid #e9ecef;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #F47C20;
      background: white;
      box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);
    }

    .form-group textarea {
      resize: vertical;
      min-height: 120px;
    }

    .required {
      color: #F47C20;
    }

    .submit-btn {
      background: linear-gradient(45deg, #F47C20, #FF8C42);
      color: white;
      border: none;
      padding: 18px 40px;
      border-radius: 50px;
      font-size: 1.1rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;
      box-shadow: 0 4px 15px rgba(244, 124, 32, 0.3);
    }

    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(244, 124, 32, 0.4);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .demo-hero h1 {
        font-size: 2.5rem;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .form-container {
        padding: 30px 20px;
        margin: 0 20px;
      }

      .support-grid {
        grid-template-columns: 1fr;
        gap: 30px;
      }

      .support-card {
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>
  <!-- Header Navigation -->
  <header class="header">
    <div class="container header-container">
      <a href="enhance_agnoshin.html" class="logo-link">
        <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="logo">
      </a>

      <nav>
        <ul class="nav-menu">
          <li><a href="enhance_agnoshin.html" class="nav-link">HOME</a></li>
          <li><a href="enhance_agnoshin.html#about" class="nav-link">ABOUT US</a></li>
          <li><a href="enhance_agnoshin.html#services" class="nav-link">SERVICES</a></li>
          <li><a href="enhance_agnoshin.html#products" class="nav-link">OUR PRODUCTS</a></li>
          <li><a href="industries.html" class="nav-link">INDUSTRIES</a></li>
          <li><a href="enhance_agnoshin.html#partners" class="nav-link">OUR PARTNERS</a></li>
          <li><a href="csr.html" class="nav-link">CSR</a></li>
        </ul>
      </nav>

      <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
      </button>

      <div class="header-actions">
        <a href="contact-us.html" class="btn btn-primary">CONTACT US</a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="demo-hero">
    <div class="container">
      <div style="display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 30px;">
        <img src="media/Agnocon logo.png" alt="AgnoCon Logo" style="height: 80px; border-radius: 8px;">
        <h1>Experience AgnoCon Live</h1>
      </div>
      <p>Discover how AgnoCon's intelligent customer experience management software can transform your contact center operations. Book a personalized demo and see the future of customer engagement in action.</p>
      <div class="demo-cta">
        <a href="#demo-form" class="btn">
          <i class="fas fa-play-circle" style="margin-right: 10px;"></i>
          Book Your Demo Now
        </a>
      </div>
    </div>
  </section>

  <!-- Support Section -->
  <section class="support-section">
    <div class="container">
      <div class="support-header">
        <h2>Our Unparalleled Customer Support Team Will Help You in Every Step of Your Journey with <span class="support-highlight">AgnoCon</span></h2>
        <p>From initial consultation to full implementation and beyond, our dedicated experts ensure your success with comprehensive support and guidance tailored to your unique business needs.</p>
      </div>

      <div class="support-grid">
        <!-- Location Support -->
        <div class="support-card">
          <div class="support-icon">
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <h3>Global Presence</h3>
          <p>With offices strategically located across multiple continents, we provide local expertise with global reach to support your contact center transformation.</p>
          <div class="contact-info">
            <p><strong>Locations:</strong><br>
            India, US, UAE, East Africa, Singapore</p>
          </div>
        </div>

        <!-- Email Support -->
        <div class="support-card">
          <div class="support-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <h3>Direct Communication</h3>
          <p>Connect directly with our AgnoCon specialists for detailed discussions about your requirements, technical questions, and implementation planning.</p>
          <div class="contact-info">
            <p><strong>Sales Inquiries:</strong><br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>Technical Support:</strong><br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>

        <!-- Phone Support -->
        <div class="support-card">
          <div class="support-icon">
            <i class="fas fa-phone"></i>
          </div>
          <h3>Immediate Assistance</h3>
          <p>Speak directly with our AgnoCon experts for immediate answers to your questions and to schedule your personalized demo session.</p>
          <div class="contact-info">
            <p><strong>Call us:</strong><br>
            <a href="tel:+************">+91 9551818896</a></p>
            <p><em>Available during business hours across all time zones</em></p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Demo Form Section -->
  <section class="demo-form-section" id="demo-form">
    <div class="container">
      <div class="form-container">
        <div class="form-header">
          <h2>Book Your AgnoCon Demo</h2>
          <p>Fill out the form below and our experts will contact you to schedule a personalized demonstration of AgnoCon's powerful features and capabilities.</p>
        </div>

        <form class="demo-form" id="demoBookingForm">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">First Name <span class="required">*</span></label>
              <input type="text" id="firstName" name="firstName" required>
            </div>
            <div class="form-group">
              <label for="lastName">Last Name <span class="required">*</span></label>
              <input type="text" id="lastName" name="lastName" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="email">Business Email <span class="required">*</span></label>
              <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number <span class="required">*</span></label>
              <input type="tel" id="phone" name="phone" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="company">Company Name <span class="required">*</span></label>
              <input type="text" id="company" name="company" required>
            </div>
            <div class="form-group">
              <label for="jobTitle">Job Title</label>
              <input type="text" id="jobTitle" name="jobTitle">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="industry">Industry</label>
              <select id="industry" name="industry">
                <option value="">Select your industry</option>
                <option value="telecommunications">Telecommunications</option>
                <option value="banking">Banking & Financial Services</option>
                <option value="healthcare">Healthcare</option>
                <option value="retail">Retail & E-commerce</option>
                <option value="transportation">Transportation & Logistics</option>
                <option value="education">Education</option>
                <option value="government">Government</option>
                <option value="insurance">Insurance</option>
                <option value="technology">Technology</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div class="form-group">
              <label for="companySize">Company Size</label>
              <select id="companySize" name="companySize">
                <option value="">Select company size</option>
                <option value="1-50">1-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-1000">201-1000 employees</option>
                <option value="1001-5000">1001-5000 employees</option>
                <option value="5000+">5000+ employees</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="requirements">Tell us about your requirements <span class="required">*</span></label>
            <textarea id="requirements" name="requirements" placeholder="Please describe your current contact center setup, challenges you're facing, and what you hope to achieve with AgnoCon..." required></textarea>
          </div>

          <div class="form-group">
            <label for="preferredTime">Preferred Demo Time</label>
            <select id="preferredTime" name="preferredTime">
              <option value="">Select preferred time</option>
              <option value="morning">Morning (9 AM - 12 PM)</option>
              <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
              <option value="evening">Evening (5 PM - 8 PM)</option>
              <option value="flexible">I'm flexible</option>
            </select>
          </div>

          <button type="submit" class="submit-btn">
            <i class="fas fa-calendar-check" style="margin-right: 10px;"></i>
            Schedule My Demo
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 40px; align-items: start;">
        <!-- Company Info Column -->
        <div class="footer-column">
          <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="footer-logo" style="height: 150px; margin-bottom: 25px; width: auto;">
          <p class="footer-description" style="font-size: 14px; line-height: 1.6; color: #555; margin: 0;">
            AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies, specializing in contact center solutions and customer experience management.
          </p>
        </div>

        <!-- Quick Links Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Quick Links</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html" style="color: #555; text-decoration: none; font-size: 14px;">Home</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#about" style="color: #555; text-decoration: none; font-size: 14px;">About Us</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#services" style="color: #555; text-decoration: none; font-size: 14px;">Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#products" style="color: #555; text-decoration: none; font-size: 14px;">Products</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#partners" style="color: #555; text-decoration: none; font-size: 14px;">Clients</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="industries.html" style="color: #555; text-decoration: none; font-size: 14px;">Industries</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="csr.html" style="color: #555; text-decoration: none; font-size: 14px;">CSR</a></li>
          </ul>
        </div>

        <!-- Services Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Services</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/professional.html" style="color: #555; text-decoration: none; font-size: 14px;">Professional Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/managed.html" style="color: #555; text-decoration: none; font-size: 14px;">Managed Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/bigdata.html" style="color: #555; text-decoration: none; font-size: 14px;">Big Data Analytics</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/cloud.html" style="color: #555; text-decoration: none; font-size: 14px;">Cloud Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/security.html" style="color: #555; text-decoration: none; font-size: 14px;">Security Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/customer-experience.html" style="color: #555; text-decoration: none; font-size: 14px;">Customer Experience</a></li>
          </ul>
        </div>

        <!-- Contact Us Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Contact Us</h3>
          <div class="footer-contact-info">
            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📞 Call us:</strong><br>
                <a href="tel:+************" style="color: #555; text-decoration: none;">+91 9551818896</a>
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Corporate Office:</strong><br>
                UAE & India & Africa
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Our Operating Continents:</strong><br>
                US, Asia Pacific, Middle East & Africa
              </p>
            </div>

            <div class="footer-contact-item">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">✉️ Mail to:</strong><br>
                <a href="mailto:<EMAIL>" style="color: #555; text-decoration: none;"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="copyright">
          © 2025 AgnoShin Technologies. All rights reserved.
        </div>

        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="advanced_interactions.js"></script>

  <!-- Demo Form JavaScript -->
  <script>
    document.getElementById('demoBookingForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(this);
      const data = {};
      for (let [key, value] of formData.entries()) {
        data[key] = value;
      }

      // Basic validation
      const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'company', 'requirements'];
      let isValid = true;

      requiredFields.forEach(field => {
        if (!data[field] || data[field].trim() === '') {
          isValid = false;
          document.getElementById(field).style.borderColor = '#dc3545';
        } else {
          document.getElementById(field).style.borderColor = '#F47C20';
        }
      });

      if (isValid) {
        // Show success message
        alert('Thank you for your interest in AgnoCon! Our team will contact you within 24 hours to schedule your personalized demo.');

        // Reset form
        this.reset();

        // Reset border colors
        requiredFields.forEach(field => {
          document.getElementById(field).style.borderColor = '#e9ecef';
        });

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        alert('Please fill in all required fields.');
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Form field focus effects
    document.querySelectorAll('.form-group input, .form-group select, .form-group textarea').forEach(field => {
      field.addEventListener('focus', function() {
        this.parentElement.style.transform = 'translateY(-2px)';
      });

      field.addEventListener('blur', function() {
        this.parentElement.style.transform = 'translateY(0)';
      });
    });
  </script>
</body>
</html>
