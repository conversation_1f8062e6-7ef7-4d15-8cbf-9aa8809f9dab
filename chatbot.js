// AgnoShin Flowise Chatbot Integration
// This file contains the complete chatbot configuration for all pages

function initializeAgnoShinChatbot() {
    console.log('Initializing AgnoShin Chatbot...');

    // Import and initialize the Flowise chatbot
    import('https://cdn.jsdelivr.net/npm/flowise-embed/dist/web.js')
        .then(({ default: Chatbot }) => {
            console.log('Chatbot module loaded successfully');
            Chatbot.init({
                chatflowid: "d2cecab1-98fe-46ed-b7e2-6a7e853f33db",
                apiHost: "https://cloud.flowiseai.com",
                chatflowConfig: {
                    /* Chatflow Config */
                },
                observersConfig: {
                    /* Observers Config */
                },
                theme: {
                    button: {
                        backgroundColor: '#F47C20',
                        right: 20,
                        bottom: 20,
                        size: 56,
                        dragAndDrop: true,
                        iconColor: 'white',
                        customIconSrc: 'https://cdn-icons-png.flaticon.com/512/2040/2040946.png',
                        autoWindowOpen: {
                            autoOpen: false,
                            openDelay: 0,
                            autoOpenOnMobile: false
                        }
                    },
                    tooltip: {
                        showTooltip: false,
                        tooltipMessage: '',
                        tooltipBackgroundColor: '#073763',
                        tooltipTextColor: 'white',
                        tooltipFontSize: 16
                    },
                    disclaimer: {
                        title: 'Welcome to AgnoShin Support',
                        message: "I'm here to help you learn more about AgnoShin's services and products. How can I assist you today?",
                        textColor: '#073763',
                        buttonColor: '#F47C20',
                        buttonText: 'Start Chatting',
                        buttonTextColor: 'white',
                        blurredBackgroundColor: 'rgba(7, 55, 99, 0.8)',
                        backgroundColor: 'white'
                    },
                    customCSS: `
                        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap');
                        
                        .flowise-chatbot {
                            font-family: 'Inter', 'Poppins', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
                        }
                        
                        .flowise-chatbot * {
                            font-family: inherit !important;
                        }
                        
                        .flowise-chatbot .chat-window {
                            border-radius: 12px !important;
                            box-shadow: 0 16px 32px rgba(7, 55, 99, 0.12), 0 6px 12px rgba(7, 55, 99, 0.08) !important;
                            border: 1px solid rgba(7, 55, 99, 0.1) !important;
                            backdrop-filter: blur(8px) !important;
                            max-height: 600px !important;
                            max-width: 380px !important;
                            min-width: 320px !important;
                        }
                        
                        .flowise-chatbot .chat-header {
                            background: linear-gradient(135deg, #073763 0%, #0B4A7A 100%) !important;
                            border-radius: 12px 12px 0 0 !important;
                            padding: 16px !important;
                            border-bottom: none !important;
                        }
                        
                        .flowise-chatbot .chat-title {
                            font-weight: 700 !important;
                            font-size: 16px !important;
                            color: white !important;
                            letter-spacing: -0.02em !important;
                        }
                        
                        @media (max-width: 768px) {
                            .flowise-chatbot .chat-window {
                                max-width: 95vw !important;
                                max-height: 80vh !important;
                                margin: 10px !important;
                            }
                        }
                        
                        .flowise-chatbot .chat-button {
                            background: linear-gradient(135deg, #F47C20 0%, #FF8C42 100%) !important;
                            border-radius: 50% !important;
                            box-shadow: 0 8px 24px rgba(244, 124, 32, 0.4) !important;
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                            border: none !important;
                        }
                        
                        .flowise-chatbot .chat-button:hover {
                            transform: translateY(-2px) scale(1.05) !important;
                            box-shadow: 0 12px 32px rgba(244, 124, 32, 0.5) !important;
                        }
                        
                        .flowise-chatbot .message-bubble {
                            border-radius: 14px !important;
                            padding: 10px 14px !important;
                            margin: 6px 0 !important;
                            font-weight: 500 !important;
                            line-height: 1.4 !important;
                            letter-spacing: -0.01em !important;
                            font-size: 14px !important;
                        }
                        
                        .flowise-chatbot .bot-message {
                            background: linear-gradient(135deg, #f7f8ff 0%, #f0f2ff 100%) !important;
                            border: 1px solid rgba(7, 55, 99, 0.08) !important;
                            color: #073763 !important;
                        }
                        
                        .flowise-chatbot .user-message {
                            background: linear-gradient(135deg, #F47C20 0%, #FF8C42 100%) !important;
                            color: white !important;
                            font-weight: 600 !important;
                        }
                        
                        .flowise-chatbot .input-container {
                            padding: 12px !important;
                            background: rgba(247, 248, 255, 0.8) !important;
                            border-radius: 0 0 12px 12px !important;
                            border-top: 1px solid rgba(7, 55, 99, 0.1) !important;
                        }
                        
                        .flowise-chatbot .text-input {
                            border-radius: 10px !important;
                            border: 2px solid rgba(7, 55, 99, 0.1) !important;
                            padding: 10px 14px !important;
                            font-weight: 500 !important;
                            transition: all 0.3s ease !important;
                            background: white !important;
                            font-size: 14px !important;
                        }
                        
                        .flowise-chatbot .text-input:focus {
                            border-color: #F47C20 !important;
                            box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1) !important;
                            outline: none !important;
                        }
                        
                        .flowise-chatbot .send-button {
                            background: linear-gradient(135deg, #F47C20 0%, #FF8C42 100%) !important;
                            border-radius: 8px !important;
                            border: none !important;
                            padding: 8px 10px !important;
                            transition: all 0.3s ease !important;
                        }
                        
                        .flowise-chatbot .send-button:hover {
                            transform: translateY(-1px) !important;
                            box-shadow: 0 4px 12px rgba(244, 124, 32, 0.3) !important;
                        }
                        
                        .flowise-chatbot .starter-prompt {
                            background: white !important;
                            border: 2px solid rgba(7, 55, 99, 0.1) !important;
                            border-radius: 10px !important;
                            padding: 8px 12px !important;
                            margin: 4px 0 !important;
                            font-weight: 500 !important;
                            color: #073763 !important;
                            transition: all 0.3s ease !important;
                            cursor: pointer !important;
                            font-size: 13px !important;
                        }
                        
                        .flowise-chatbot .starter-prompt:hover {
                            border-color: #F47C20 !important;
                            background: rgba(244, 124, 32, 0.05) !important;
                            transform: translateY(-1px) !important;
                        }
                        
                        .flowise-chatbot .welcome-message {
                            font-weight: 600 !important;
                            font-size: 16px !important;
                            line-height: 1.6 !important;
                            color: #073763 !important;
                        }
                        
                        .flowise-chatbot .disclaimer {
                            border-radius: 16px !important;
                            background: white !important;
                            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
                            border: 1px solid rgba(7, 55, 99, 0.1) !important;
                        }
                        
                        .flowise-chatbot .disclaimer-title {
                            font-weight: 700 !important;
                            font-size: 20px !important;
                            color: #073763 !important;
                            margin-bottom: 12px !important;
                        }
                        
                        .flowise-chatbot .disclaimer-button {
                            background: linear-gradient(135deg, #F47C20 0%, #FF8C42 100%) !important;
                            border-radius: 12px !important;
                            padding: 12px 24px !important;
                            font-weight: 600 !important;
                            border: none !important;
                            transition: all 0.3s ease !important;
                        }
                        
                        .flowise-chatbot .disclaimer-button:hover {
                            transform: translateY(-2px) !important;
                            box-shadow: 0 8px 20px rgba(244, 124, 32, 0.3) !important;
                        }
                        
                        /* Hide Process Flow and Sources - Aggressive Approach */
                        .flowise-chatbot *:contains("Process Flow"),
                        .flowise-chatbot *:contains("Start"),
                        .flowise-chatbot *:contains("Sources:"),
                        .flowise-chatbot *:contains("AgnoCon"),
                        .flowise-chatbot *:contains("WhatsA"),
                        .flowise-chatbot *:contains("erations"),
                        .flowise-chatbot *:contains("Predicti") {
                            display: none !important;
                            visibility: hidden !important;
                            opacity: 0 !important;
                            height: 0 !important;
                            max-height: 0 !important;
                            overflow: hidden !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        
                        .flowise-chatbot [class*="MuiAccordion"],
                        .flowise-chatbot [class*="accordion"],
                        .flowise-chatbot [class*="Accordion"],
                        .flowise-chatbot [class*="collaps"],
                        .flowise-chatbot [class*="Collaps"],
                        .flowise-chatbot [class*="expand"],
                        .flowise-chatbot [class*="Expand"],
                        .flowise-chatbot div[role="button"],
                        .flowise-chatbot button[aria-expanded],
                        .flowise-chatbot [aria-expanded="true"],
                        .flowise-chatbot [aria-expanded="false"],
                        .flowise-chatbot [class*="chip"],
                        .flowise-chatbot [class*="Chip"],
                        .flowise-chatbot [class*="tag"],
                        .flowise-chatbot [class*="Tag"],
                        .flowise-chatbot [class*="badge"],
                        .flowise-chatbot [class*="Badge"],
                        .flowise-chatbot [class*="process"],
                        .flowise-chatbot [class*="Process"],
                        .flowise-chatbot [class*="flow"],
                        .flowise-chatbot [class*="Flow"],
                        .flowise-chatbot [class*="source"],
                        .flowise-chatbot [class*="Source"] {
                            display: none !important;
                            visibility: hidden !important;
                            opacity: 0 !important;
                            height: 0 !important;
                            max-height: 0 !important;
                            overflow: hidden !important;
                        }
                    `,
                    chatWindow: {
                        showTitle: true,
                        showAgentMessages: true,
                        title: '💬 AgnoShin Assistant',
                        titleAvatarSrc: 'https://cdn-icons-png.flaticon.com/512/2040/2040946.png',
                        welcomeMessage: '👋 Hello! Welcome to AgnoShin.\\n\\nI\\'m your AI assistant, ready to help you discover our innovative technology solutions, comprehensive services, and cutting-edge products.\\n\\n✨ How can I assist you today?',
                        errorMessage: '⚠️ I apologize, but I\\'m experiencing some technical difficulties. Please try again in a moment or contact our team <NAME_EMAIL> for immediate assistance.',
                        backgroundColor: '#ffffff',
                        backgroundImage: 'linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%)',
                        height: 600,
                        width: 380,
                        fontSize: 14,
                        starterPrompts: [
                            "🚀 What services does AgnoShin offer?",
                            "💼 Tell me about your products",
                            "🎯 How can AgnoShin help my business?",
                            "⚡ What is AgnoCon?",
                            "☁️ Cloud Services & Solutions",
                            "🔒 Security & Data Protection"
                        ],
                        starterPromptFontSize: 13,
                        clearChatOnReload: true,
                        sourceDocsTitle: '',
                        renderHTML: true,
                        botMessage: {
                            backgroundColor: 'linear-gradient(135deg, #f7f8ff 0%, #f0f2ff 100%)',
                            textColor: '#073763',
                            showAvatar: true,
                            avatarSrc: 'https://cdn-icons-png.flaticon.com/512/4712/4712027.png'
                        },
                        userMessage: {
                            backgroundColor: 'linear-gradient(135deg, #F47C20 0%, #FF8C42 100%)',
                            textColor: '#ffffff',
                            showAvatar: true,
                            avatarSrc: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png'
                        },
                        textInput: {
                            placeholder: '💬 Ask me about AgnoShin services, products, or solutions...',
                            backgroundColor: '#ffffff',
                            textColor: '#073763',
                            sendButtonColor: '#F47C20',
                            maxChars: 500,
                            maxCharsWarningMessage: '⚠️ Please keep your message under 500 characters for better response quality.',
                            autoFocus: true,
                            sendMessageSound: false,
                            sendSoundLocation: '',
                            receiveMessageSound: false,
                            receiveSoundLocation: ''
                        },
                        feedback: {
                            color: '#073763'
                        },
                        dateTimeToggle: {
                            date: true,
                            time: true
                        },
                        footer: {
                            textColor: '#073763',
                            text: '⚡ Powered by',
                            company: 'AgnoShin AI',
                            companyLink: 'https://agnoshin.com'
                        }
                    }
                }
            });

            // Additional JavaScript to hide process flow elements
            setTimeout(() => {
                const hideElements = () => {
                    const textToHide = ['Process Flow', 'Start', 'Sources:', 'AgnoCon', 'WhatsA', 'erations', 'Predicti'];
                    
                    textToHide.forEach(text => {
                        const elements = document.querySelectorAll('*');
                        elements.forEach(el => {
                            if (el.textContent && el.textContent.includes(text)) {
                                el.style.display = 'none';
                                el.style.visibility = 'hidden';
                                el.style.opacity = '0';
                                el.style.height = '0';
                                el.style.maxHeight = '0';
                                el.style.overflow = 'hidden';
                                el.style.margin = '0';
                                el.style.padding = '0';
                            }
                        });
                    });
                    
                    const accordionSelectors = [
                        '[class*="MuiAccordion"]', '[class*="accordion"]', '[class*="Accordion"]',
                        '[class*="collaps"]', '[class*="Collaps"]', '[class*="expand"]', '[class*="Expand"]',
                        '[role="button"]', '[aria-expanded]', '[class*="chip"]', '[class*="Chip"]',
                        '[class*="tag"]', '[class*="Tag"]', '[class*="process"]', '[class*="Process"]',
                        '[class*="flow"]', '[class*="Flow"]', '[class*="source"]', '[class*="Source"]'
                    ];
                    
                    accordionSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.display = 'none';
                            el.style.visibility = 'hidden';
                            el.style.opacity = '0';
                            el.style.height = '0';
                            el.style.maxHeight = '0';
                            el.style.overflow = 'hidden';
                        });
                    });
                };
                
                hideElements();
                setInterval(hideElements, 1000);
                
                const observer = new MutationObserver(hideElements);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
            }, 2000);
        })
        .catch(error => {
            console.error('Failed to load chatbot:', error);
            // Fallback: Try direct script loading
            const script = document.createElement('script');
            script.type = 'module';
            script.innerHTML = `
                import Chatbot from 'https://cdn.jsdelivr.net/npm/flowise-embed/dist/web.js';
                Chatbot.init({
                    chatflowid: "d2cecab1-98fe-46ed-b7e2-6a7e853f33db",
                    apiHost: "https://cloud.flowiseai.com",
                    theme: {
                        button: {
                            backgroundColor: '#F47C20',
                            right: 20,
                            bottom: 20,
                            size: 56,
                            iconColor: 'white'
                        },
                        chatWindow: {
                            title: '💬 AgnoShin Assistant',
                            welcomeMessage: '👋 Hello! Welcome to AgnoShin. How can I assist you today?'
                        }
                    }
                });
            `;
            document.head.appendChild(script);
        });
}

// Initialize chatbot when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAgnoShinChatbot);
} else {
    initializeAgnoShinChatbot();
}
