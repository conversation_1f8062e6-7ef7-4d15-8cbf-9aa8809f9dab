// AgnoShin Flowise Chatbot Integration
// This file contains the complete chatbot configuration for all pages

function initializeAgnoShinChatbot() {
    // Import and initialize the Flowise chatbot
    import('https://cdn.jsdelivr.net/npm/flowise-embed/dist/web.js')
        .then(({ default: Chatbot }) => {
            Chatbot.init({
                chatflowid: "d2cecab1-98fe-46ed-b7e2-6a7e853f33db",
                apiHost: "https://cloud.flowiseai.com",
                chatflowConfig: {
                    /* Chatflow Config */
                },
                observersConfig: {
                    /* Observers Config */
                },
                theme: {
                    button: {
                        backgroundColor: '#3B81F6',
                        right: 20,
                        bottom: 20,
                        size: 48,
                        dragAndDrop: true,
                        iconColor: 'white',
                        customIconSrc: 'https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/google-messages.svg',
                        autoWindowOpen: {
                            autoOpen: true,
                            openDelay: 2,
                            autoOpenOnMobile: false
                        }
                    },
                    tooltip: {
                        showTooltip: true,
                        tooltipMessage: 'Hi There 👋!',
                        tooltipBackgroundColor: 'black',
                        tooltipTextColor: 'white',
                        tooltipFontSize: 16
                    },
                    disclaimer: {
                        title: 'Disclaimer',
                        message: "By using this chatbot, you agree to the <a target=\"_blank\" href=\"https://flowiseai.com/terms\">Terms & Condition</a>",
                        textColor: 'black',
                        buttonColor: '#3b82f6',
                        buttonText: 'Start Chatting',
                        buttonTextColor: 'white',
                        blurredBackgroundColor: 'rgba(0, 0, 0, 0.4)',
                        backgroundColor: 'white'
                    },
                    customCSS: ``,
                    chatWindow: {
                        showTitle: true,
                        showAgentMessages: true,
                        title: 'Flowise Bot',
                        titleAvatarSrc: 'https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/google-messages.svg',
                        welcomeMessage: 'Hello! This is custom welcome message',
                        errorMessage: 'This is a custom error message',
                        backgroundColor: '#ffffff',
                        backgroundImage: 'enter image path or link',
                        height: 700,
                        width: 400,
                        fontSize: 16,
                        starterPrompts: [
                            "What is a bot?",
                            "Who are you?"
                        ],
                        starterPromptFontSize: 15,
                        clearChatOnReload: false,
                        sourceDocsTitle: 'Sources:',
                        renderHTML: true,
                        botMessage: {
                            backgroundColor: '#f7f8ff',
                            textColor: '#303235',
                            showAvatar: true,
                            avatarSrc: 'https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/parroticon.png'
                        },
                        userMessage: {
                            backgroundColor: '#3B81F6',
                            textColor: '#ffffff',
                            showAvatar: true,
                            avatarSrc: 'https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/usericon.png'
                        },
                        textInput: {
                            placeholder: 'Type your question',
                            backgroundColor: '#ffffff',
                            textColor: '#303235',
                            sendButtonColor: '#3B81F6',
                            maxChars: 50,
                            maxCharsWarningMessage: 'You exceeded the characters limit. Please input less than 50 characters.',
                            autoFocus: true,
                            sendMessageSound: true,
                            sendSoundLocation: 'send_message.mp3',
                            receiveMessageSound: true,
                            receiveSoundLocation: 'receive_message.mp3'
                        },
                        feedback: {
                            color: '#303235'
                        },
                        dateTimeToggle: {
                            date: true,
                            time: true
                        },
                        footer: {
                            textColor: '#303235',
                            text: 'Powered by',
                            company: 'Flowise',
                            companyLink: 'https://flowiseai.com'
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Failed to load chatbot:', error);
        });
}

// Initialize chatbot when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAgnoShinChatbot);
} else {
    initializeAgnoShinChatbot();
}
