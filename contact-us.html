<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us - AgnoShin</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="enhance_style.css">
  <link rel="stylesheet" href="enhanced_ui.css">

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- AOS (Animate On Scroll) Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Contact Us Page Styles -->
  <style>
    /* Hero Section */
    .contact-hero {
      background: linear-gradient(135deg, #073763 0%, #F47C20 100%);
      color: white;
      padding: 120px 0;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .contact-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at center, rgba(244, 124, 32, 0.1) 0%, transparent 70%);
    }

    .contact-hero h1 {
      color: white;
      font-size: 3.5rem;
      margin-bottom: 20px;
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      position: relative;
      z-index: 1;
    }

    .contact-hero p {
      font-size: 1.2rem;
      opacity: 0.9;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
      position: relative;
      z-index: 1;
    }

    /* Contact Form Section */
    .contact-section {
      padding: 100px 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .contact-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 60px;
      align-items: start;
    }

    /* Contact Form Styling */
    .contact-form {
      background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
      padding: 50px;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.1);
      border: 1px solid rgba(244, 124, 32, 0.1);
      position: relative;
      overflow: hidden;
    }

    .contact-form::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(244, 124, 32, 0.05) 0%, transparent 70%);
      pointer-events: none;
    }

    .contact-form h2 {
      color: #073763;
      font-size: 2rem;
      margin-bottom: 10px;
      position: relative;
      z-index: 1;
    }

    .contact-form .subtitle {
      color: #666;
      font-size: 1rem;
      margin-bottom: 30px;
      line-height: 1.6;
      position: relative;
      z-index: 1;
    }

    .form-group {
      margin-bottom: 25px;
      position: relative;
      z-index: 1;
    }

    .form-group label {
      display: block;
      color: #073763;
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 1rem;
    }

    .form-group input,
    .form-group textarea {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9);
    }

    .form-group input:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #F47C20;
      box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);
      transform: translateY(-2px);
    }

    .form-group textarea {
      resize: vertical;
      min-height: 120px;
    }

    .submit-btn {
      background: linear-gradient(135deg, #F47C20 0%, #FF8C42 100%);
      color: white;
      padding: 15px 40px;
      border: none;
      border-radius: 12px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 8px 25px rgba(244, 124, 32, 0.3);
      position: relative;
      z-index: 1;
    }

    .submit-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 35px rgba(244, 124, 32, 0.4);
    }

    /* Contact Info Section */
    .contact-info {
      background: linear-gradient(145deg, #073763 0%, #0B2A5A 100%);
      padding: 50px;
      border-radius: 20px;
      color: white;
      box-shadow: 0 20px 60px rgba(7, 55, 99, 0.3);
      position: relative;
      overflow: hidden;
    }

    .contact-info::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(244, 124, 32, 0.1) 0%, transparent 70%);
      pointer-events: none;
    }

    .contact-info h2 {
      color: white;
      font-size: 2rem;
      margin-bottom: 10px;
      position: relative;
      z-index: 1;
    }

    .contact-info .subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1rem;
      margin-bottom: 30px;
      line-height: 1.6;
      position: relative;
      z-index: 1;
    }

    .contact-item {
      margin-bottom: 35px;
      position: relative;
      z-index: 1;
    }

    .contact-item h3 {
      color: #F47C20;
      font-size: 1.3rem;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .contact-item h3 i {
      margin-right: 12px;
      font-size: 1.5rem;
    }

    .contact-item p {
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.6;
      margin-left: 35px;
    }

    .contact-item a {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .contact-item a:hover {
      color: #F47C20;
    }

    /* Office Locations */
    .office-location {
      background: rgba(244, 124, 32, 0.1);
      padding: 25px;
      border-radius: 15px;
      margin-bottom: 25px;
      border-left: 4px solid #F47C20;
      position: relative;
      z-index: 1;
    }

    .office-location h4 {
      color: #F47C20;
      font-size: 1.2rem;
      margin-bottom: 10px;
      font-weight: 600;
    }

    .office-location p {
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.5;
      margin: 0;
    }

    /* Global Offices Section */
    .offices-section {
      padding: 100px 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .section-header {
      text-align: center;
      margin-bottom: 60px;
    }

    .section-header h2 {
      color: #073763;
      font-size: 2.5rem;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .section-header p {
      color: #666;
      font-size: 1.1rem;
      max-width: 600px;
      margin: 0 auto;
    }

    .offices-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 25px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .office-card {
      background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 15px 50px rgba(0,0,0,0.1);
      border: 1px solid rgba(244, 124, 32, 0.1);
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
      opacity: 0;
      transform: translateY(30px);
    }

    .office-card.aos-animate {
      opacity: 1;
      transform: translateY(0);
    }

    .office-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 25px 70px rgba(0,0,0,0.15);
      border-color: rgba(244, 124, 32, 0.3);
    }

    .office-card::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(244, 124, 32, 0.05) 0%, transparent 70%);
      pointer-events: none;
      transition: all 0.4s ease;
    }

    .office-card:hover::before {
      background: radial-gradient(circle, rgba(244, 124, 32, 0.1) 0%, transparent 70%);
    }

    .office-flag {
      text-align: center;
      margin-bottom: 25px;
      position: relative;
      z-index: 1;
    }

    .flag-icon {
      width: 60px;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
      transition: transform 0.3s ease;
    }

    .office-card:hover .flag-icon {
      transform: scale(1.1);
    }

    .office-content {
      position: relative;
      z-index: 1;
    }

    .office-content h3 {
      color: #073763;
      font-size: 1.3rem;
      margin-bottom: 20px;
      text-align: center;
      font-weight: 600;
    }

    .detail-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      padding: 12px 0;
      border-bottom: 1px solid rgba(244, 124, 32, 0.1);
    }

    .detail-item:last-child {
      border-bottom: none;
    }

    .detail-item i {
      color: #F47C20;
      font-size: 1.1rem;
      margin-right: 15px;
      margin-top: 3px;
      min-width: 20px;
    }

    .detail-item span {
      color: #333;
      line-height: 1.5;
      font-size: 0.95rem;
    }

    .detail-item a {
      color: #073763;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .detail-item a:hover {
      color: #F47C20;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .contact-hero h1 {
        font-size: 3rem;
        letter-spacing: -0.5px;
      }

      .contact-hero p {
        font-size: 1.2rem;
      }

      .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
      }

      .contact-form,
      .contact-info {
        padding: 30px;
      }

      .contact-section {
        padding: 60px 0;
      }

      .offices-section {
        padding: 60px 0;
      }

      .section-header h2 {
        font-size: 2.2rem;
      }

      .offices-grid {
        grid-template-columns: 1fr;
        gap: 25px;
      }

      .office-card {
        padding: 30px;
      }
    }

    @media (max-width: 1200px) {
      .offices-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }
    }

    @media (max-width: 900px) {
      .offices-grid {
        grid-template-columns: 1fr;
        gap: 25px;
      }
    }
  </style>
</head>
<body>
  <!-- Header Navigation -->
  <header class="header">
    <div class="container header-container">
      <a href="enhance_agnoshin.html" class="logo-link">
        <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="logo">
      </a>

      <nav>
        <ul class="nav-menu">
          <li><a href="enhance_agnoshin.html" class="nav-link">HOME</a></li>
          <li><a href="enhance_agnoshin.html#about" class="nav-link">ABOUT US</a></li>
          <li><a href="enhance_agnoshin.html#services" class="nav-link">SERVICES</a></li>
          <li><a href="enhance_agnoshin.html#products" class="nav-link">OUR PRODUCTS</a></li>
          <li><a href="industries.html" class="nav-link">INDUSTRIES</a></li>
          <li><a href="enhance_agnoshin.html#partners" class="nav-link">OUR CLIENTS</a></li>
          <li><a href="csr.html" class="nav-link">CSR</a></li>
        </ul>
      </nav>

      <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
      </button>

      <div class="header-actions">
        <a href="contact-us.html" class="btn btn-primary active">CONTACT US</a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="contact-hero">
    <div class="container">
      <h1>Let's Connect</h1>
      <p>Your comments, feedback, and queries are important to us. We are here to answer your questions and provide you with more information about your business future.</p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="contact-container">
      <div class="contact-grid">
        <!-- Contact Form -->
        <div class="contact-form">
          <h2>Leave Your Comment</h2>
          <p class="subtitle">Reach out to start a discussion about your business, its future, and how we can help.</p>

          <form id="contactForm">
            <div class="form-group">
              <label for="name">Name *</label>
              <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
              <label for="email">Email *</label>
              <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
              <label for="comments">Comments *</label>
              <textarea id="comments" name="comments" placeholder="Tell us about your requirements..." required></textarea>
            </div>

            <button type="submit" class="submit-btn">
              <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>
              Submit
            </button>
          </form>
        </div>

        <!-- Contact Information -->
        <div class="contact-info">
          <h2>Let's Get In Touch</h2>
          <p class="subtitle">We're here to help you transform your contact center operations with cutting-edge technology solutions.</p>

          <div class="contact-item">
            <h3><i class="fas fa-phone"></i>Call us:</h3>
            <p><a href="tel:+************">+91 9551818896</a></p>
          </div>

          <div class="contact-item">
            <h3><i class="fas fa-envelope"></i>Mail to:</h3>
            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>

          <div class="contact-item">
            <h3><i class="fas fa-map-marker-alt"></i>Our Global Offices:</h3>
            <p style="margin-bottom: 30px; margin-left: 35px;">Visit us at any of our global locations</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Global Offices Section -->
  <section class="offices-section">
    <div class="container">
      <div class="section-header">
        <h2>Our Global Presence</h2>
        <p>Connect with us at any of our worldwide locations</p>
      </div>

      <div class="offices-grid">
        <!-- India Office -->
        <div class="office-card" data-aos="fade-up" data-aos-delay="100">
          <div class="office-flag">
            <img src="https://flagcdn.com/w80/in.png" alt="India Flag" class="flag-icon">
          </div>
          <div class="office-content">
            <h3>Corporate Office - India</h3>
            <div class="office-details">
              <div class="detail-item">
                <i class="fas fa-building"></i>
                <span>AgnoShin Technologies Private Limited</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>Manasarovar, #1, 9C/10A, Second Floor, Second Street,<br>
                Ayodhya Colony, Velachery, Chennai, Tamil Nadu 600042</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-phone"></i>
                <a href="tel:+************">+91 9551818896</a>
              </div>
              <div class="detail-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>

        <!-- USA Office -->
        <div class="office-card" data-aos="fade-up" data-aos-delay="200">
          <div class="office-flag">
            <img src="https://flagcdn.com/w80/us.png" alt="USA Flag" class="flag-icon">
          </div>
          <div class="office-content">
            <h3>USA Office</h3>
            <div class="office-details">
              <div class="detail-item">
                <i class="fas fa-building"></i>
                <span>AgnoShin Inc</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>815, Brazos St., Ste. 500<br>
                Austin TX 78701</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>

        <!-- Singapore Office -->
        <div class="office-card" data-aos="fade-up" data-aos-delay="300">
          <div class="office-flag">
            <img src="https://flagcdn.com/w80/sg.png" alt="Singapore Flag" class="flag-icon">
          </div>
          <div class="office-content">
            <h3>Singapore Office</h3>
            <div class="office-details">
              <div class="detail-item">
                <i class="fas fa-building"></i>
                <span>AgnoShin Pte Ltd</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>68, Circular Road, #02–01<br>
                Singapore (049422)</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>

        <!-- Kenya Office -->
        <div class="office-card" data-aos="fade-up" data-aos-delay="400">
          <div class="office-flag">
            <img src="https://flagcdn.com/w80/ke.png" alt="Kenya Flag" class="flag-icon">
          </div>
          <div class="office-content">
            <h3>Kenya Office</h3>
            <div class="office-details">
              <div class="detail-item">
                <i class="fas fa-building"></i>
                <span>AgnoShin Consulting Limited</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>3rd floor, One Park Avenue Building,<br>
                1st Parklands Avenue Building, 1st Avenue,<br>
                off Limuru Road, Nairobi</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 40px; align-items: start;">
        <!-- Company Info Column -->
        <div class="footer-column">
          <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="footer-logo" style="height: 150px; margin-bottom: 25px; width: auto;">
          <p class="footer-description" style="font-size: 14px; line-height: 1.6; color: #555; margin: 0;">
            AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.
          </p>
        </div>

        <!-- Quick Links Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Quick Links</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html" style="color: #555; text-decoration: none; font-size: 14px;">Home</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#about" style="color: #555; text-decoration: none; font-size: 14px;">About Us</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#services" style="color: #555; text-decoration: none; font-size: 14px;">Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#products" style="color: #555; text-decoration: none; font-size: 14px;">Products</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="enhance_agnoshin.html#partners" style="color: #555; text-decoration: none; font-size: 14px;">Clients</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="industries.html" style="color: #555; text-decoration: none; font-size: 14px;">Industries</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="csr.html" style="color: #555; text-decoration: none; font-size: 14px;">CSR</a></li>
          </ul>
        </div>

        <!-- Services Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Services</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/professional.html" style="color: #555; text-decoration: none; font-size: 14px;">Professional Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/managed.html" style="color: #555; text-decoration: none; font-size: 14px;">Managed Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/bigdata.html" style="color: #555; text-decoration: none; font-size: 14px;">Big Data Analytics</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/cloud.html" style="color: #555; text-decoration: none; font-size: 14px;">Cloud Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/security.html" style="color: #555; text-decoration: none; font-size: 14px;">Security Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/customer-experience.html" style="color: #555; text-decoration: none; font-size: 14px;">Customer Experience</a></li>
          </ul>
        </div>

        <!-- Contact Us Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Contact Us</h3>
          <div class="footer-contact-info">
            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📞 Call us:</strong><br>
                <a href="tel:+************" style="color: #555; text-decoration: none;">+91 9551818896</a>
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Corporate Office:</strong><br>
                UAE & India & Africa
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Our Operating Continents:</strong><br>
                US, Asia Pacific, Middle East & Africa
              </p>
            </div>

            <div class="footer-contact-item">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">✉️ Mail to:</strong><br>
                <a href="mailto:<EMAIL>" style="color: #555; text-decoration: none;"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="copyright">
          © 2025 AgnoShin Technologies. All rights reserved.
        </div>

        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="advanced_interactions.js"></script>

  <!-- AgnoShin Chatbot -->
  <script src="chatbot.js"></script>

  <!-- AOS (Animate On Scroll) Library -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

  <!-- Contact Form JavaScript -->
  <script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const name = document.getElementById('name').value;
      const email = document.getElementById('email').value;
      const comments = document.getElementById('comments').value;

      // Simple validation
      if (name && email && comments) {
        alert('Thank you for your message! We will get back to you soon.');
        this.reset();
      } else {
        alert('Please fill in all required fields.');
      }
    });

    // Initialize AOS (Animate On Scroll)
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });

    // Custom animation for office cards
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('aos-animate');
        }
      });
    }, observerOptions);

    // Observe all office cards
    document.querySelectorAll('.office-card').forEach(card => {
      observer.observe(card);
    });
  </script>
</body>
</html>
