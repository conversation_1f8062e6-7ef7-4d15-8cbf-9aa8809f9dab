<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AgnoShin - Technology Solutions</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="enhance_style.css">
  <link rel="stylesheet" href="enhanced_ui.css">

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <!-- Header Navigation -->
  <header class="header">
    <div class="container header-container">
      <a href="#" class="logo-link">
        <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="logo">
      </a>

      <nav>
        <ul class="nav-menu">
          <li><a href="#" class="nav-link active">HOME</a></li>
          <li><a href="#about" class="nav-link">ABOUT US</a></li>
          <li><a href="#services" class="nav-link">SERVICES</a></li>
          <li><a href="#products" class="nav-link">OUR PRODUCTS</a></li>
          <li><a href="industries.html" class="nav-link">INDUSTRIES</a></li>
          <li><a href="#partners" class="nav-link">OUR CLIENTS</a></li>
          <li><a href="csr.html" class="nav-link">CSR</a></li>
        </ul>
      </nav>

      <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
      </button>

      <div class="header-actions">
        <a href="contact-us.html" class="btn btn-primary">CONTACT US</a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">Build universally relevant <span class="gradient-text">technologies</span> of accommodation</h1>
        <p class="hero-subtitle">AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.</p>
        <div class="hero-buttons">
          <a href="#services" class="btn btn-primary">Explore Our Services</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Main App Grid Section -->
  <section class="section">
    <div class="container">
      <div class="app-grid">
        <!-- Professional Services -->
        <div class="app-card">
          <a href="services/professional.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M22 42C22 39.7909 23.7909 38 26 38H38C40.2091 38 42 39.7909 42 42V44C42 44.5523 41.5523 45 41 45H23C22.4477 45 22 44.5523 22 44V42Z" fill="#073763"/>
                <circle cx="32" cy="28" r="8" fill="#F47C20"/>
                <path d="M32 36C36.4183 36 40 32.4183 40 28C40 23.5817 36.4183 20 32 20" stroke="#0B2A5A" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Professional Services</h3>
          </a>
        </div>

        <!-- Managed Services -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M32 20C25.3726 20 20 25.3726 20 32C20 38.6274 25.3726 44 32 44C38.6274 44 44 38.6274 44 32" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
              <path d="M32 28L36 32L32 36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M24 32H36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">Managed Services</h3>
        </div>

        <!-- Big Data Analytics -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="20" y="20" width="24" height="24" rx="2" fill="#0B2A5A"/>
              <rect x="24" y="36" width="4" height="4" fill="#F47C20"/>
              <rect x="30" y="32" width="4" height="8" fill="#F47C20"/>
              <rect x="36" y="28" width="4" height="12" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Big Data Analytics</h3>
        </div>

        <!-- Cloud Services -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M44 36C46.2091 36 48 34.2091 48 32C48 29.7909 46.2091 28 44 28C43.8178 28 43.6382 28.0116 43.4615 28.0342C42.7523 24.1101 39.3979 21.1436 35.2222 21.0038C31.7211 20.8866 28.5486 22.7881 27.0614 25.6492C26.7708 25.5563 26.4642 25.5 26.1481 25.5C24.4097 25.5 23 26.8431 23 28.5C23 28.6929 23.0176 28.8821 23.0515 29.0661C20.7823 29.6402 19 31.6543 19 34C19 36.7614 21.2386 39 24 39H44Z" fill="#0B2A5A"/>
              <path d="M34 39L34 45" stroke="#F47C20" stroke-width="3" stroke-linecap="round"/>
              <path d="M30 43L34 47L38 43" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">Cloud Services</h3>
        </div>

        <!-- Security Services -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M32 20L20 24V32C20 39.732 25.36 47.036 32 48C38.64 47.036 44 39.732 44 32V24L32 20Z" fill="#0B2A5A"/>
              <path d="M28 32L30.5 34.5L36 29" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">Security Services</h3>
        </div>

        <!-- Customer Experience -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M44 32C44 25.3726 38.6274 20 32 20C25.3726 20 20 25.3726 20 32C20 38.6274 25.3726 44 32 44" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
              <path d="M32 28V32L36 36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="44" cy="44" r="4" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Customer Experience</h3>
        </div>

        <!-- AgnoCon -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M20 32C20 25.3726 25.3726 20 32 20C38.6274 20 44 25.3726 44 32C44 38.6274 38.6274 44 32 44C25.3726 44 20 38.6274 20 32Z" fill="#F47C20"/>
              <path d="M32 28V36" stroke="white" stroke-width="3" stroke-linecap="round"/>
              <path d="M28 32H36" stroke="white" stroke-width="3" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">AgnoCon</h3>
        </div>

        <!-- Omni Channel -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M44 32H20" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
              <path d="M32 20V44" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
              <circle cx="32" cy="32" r="6" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Omni Channel Support</h3>
        </div>

        <!-- Campaign Management -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M24 20L32 28L40 20" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M40 44L32 36L24 44" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20 32H44" stroke="#F47C20" stroke-width="3" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">Campaign Management</h3>
        </div>

        <!-- Real-Time Dashboard -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="20" y="20" width="10" height="10" rx="2" fill="#0B2A5A"/>
              <rect x="34" y="20" width="10" height="10" rx="2" fill="#0B2A5A"/>
              <rect x="20" y="34" width="10" height="10" rx="2" fill="#0B2A5A"/>
              <rect x="34" y="34" width="10" height="10" rx="2" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Real-Time Dashboard</h3>
        </div>

        <!-- Analytics -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="22" y="36" width="4" height="8" rx="1" fill="#0B2A5A"/>
              <rect x="30" y="32" width="4" height="12" rx="1" fill="#0B2A5A"/>
              <rect x="38" y="28" width="4" height="16" rx="1" fill="#F47C20"/>
              <path d="M20 24L44 24" stroke="#0B2A5A" stroke-width="2" stroke-linecap="round"/>
              <path d="M20 20L44 20" stroke="#0B2A5A" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">Analytics</h3>
        </div>

        <!-- Great Place to Work -->
        <div class="app-card">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M32 20L35.8167 27.7833L44.5 28.9167L38.25 34.9833L39.6333 43.6167L32 39.5L24.3667 43.6167L25.75 34.9833L19.5 28.9167L28.1833 27.7833L32 20Z" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Great Place to Work</h3>
        </div>
      </div>

      <div class="view-all">
        <a href="#" class="view-all-link">
          View all Services
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 10H16M16 10L10 4M16 10L10 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </a>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section about">
    <div class="container">
      <h2 class="section-title">About us</h2>

      <div class="about-content">
        <div class="about-image" style="background-color: #f5f5f5; display: flex; justify-content: center; align-items: center; border-radius: 8px; padding: 20px;">
          <div style="text-align: center;">
            <img src="media/Ast_team.jpeg" alt="Our Team" style="max-width: 100%; height: auto; border-radius: 8px;">
            <h3 style="margin-top: 10px; color: #073763;">Our Team</h3>
          </div>
        </div>

        <div class="about-text">
          <p>AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.</p>

          <p>AgnoShin being a corporate organization focuses on providing comprehensive and customizable solutions in customer experience management. We are strategically established with professional experts to formulate innovative ideas to provide products and services of high standards.</p>

          <a href="#" class="btn btn-primary">Learn More</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Partners Section -->
  <section id="partners" class="section partners">
    <div class="container">
      <h2 class="section-title">Our Clients</h2>
      <p class="section-subtitle">We work together and achieve our common remarkable goals.</p>

      <div class="marquee-container">
        <div class="marquee-content">
          <img src="media/clients/image (1).png" alt="Client 1" class="client-logo">
          <img src="media/clients/image (2).png" alt="Client 2" class="client-logo">
          <img src="media/clients/image (3).png" alt="Client 3" class="client-logo">
          <img src="media/clients/image (4).png" alt="Client 4" class="client-logo">
          <img src="media/clients/image (5).png" alt="Client 5" class="client-logo">
          <img src="media/clients/image (6).png" alt="Client 6" class="client-logo">
          <img src="media/clients/image (7).png" alt="Client 7" class="client-logo">
          <img src="media/clients/image (8).png" alt="Client 8" class="client-logo">
          <img src="media/clients/image (9).png" alt="Client 9" class="client-logo">
          <img src="media/clients/image (10).png" alt="Client 10" class="client-logo">
          <img src="media/clients/image (11).png" alt="Client 11" class="client-logo">
          <img src="media/clients/image (12).png" alt="Client 12" class="client-logo">
          <img src="media/clients/image (13).png" alt="Client 13" class="client-logo">
          <img src="media/clients/image (14).png" alt="Client 14" class="client-logo">
          <img src="media/clients/image (15).png" alt="Client 15" class="client-logo">

          <!-- Duplicate for seamless loop -->
          <img src="media/clients/image (1).png" alt="Client 1" class="client-logo">
          <img src="media/clients/image (2).png" alt="Client 2" class="client-logo">
          <img src="media/clients/image (3).png" alt="Client 3" class="client-logo">
          <img src="media/clients/image (4).png" alt="Client 4" class="client-logo">
          <img src="media/clients/image (5).png" alt="Client 5" class="client-logo">
          <img src="media/clients/image (6).png" alt="Client 6" class="client-logo">
          <img src="media/clients/image (7).png" alt="Client 7" class="client-logo">
          <img src="media/clients/image (8).png" alt="Client 8" class="client-logo">
          <img src="media/clients/image (9).png" alt="Client 9" class="client-logo">
          <img src="media/clients/image (10).png" alt="Client 10" class="client-logo">
          <img src="media/clients/image (11).png" alt="Client 11" class="client-logo">
          <img src="media/clients/image (12).png" alt="Client 12" class="client-logo">
          <img src="media/clients/image (13).png" alt="Client 13" class="client-logo">
          <img src="media/clients/image (14).png" alt="Client 14" class="client-logo">
          <img src="media/clients/image (15).png" alt="Client 15" class="client-logo">
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="section">
    <div class="container">
      <h2 class="section-title">Our Services</h2>
      <p class="section-subtitle">AgnoShin's experts develop, enhance, and maintain your applications, irrespective of the technology used.</p>

      <div class="app-grid">
        <!-- Professional Services -->
        <div class="app-card">
          <a href="services/professional.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M22 42C22 39.7909 23.7909 38 26 38H38C40.2091 38 42 39.7909 42 42V44C42 44.5523 41.5523 45 41 45H23C22.4477 45 22 44.5523 22 44V42Z" fill="#073763"/>
                <circle cx="32" cy="28" r="8" fill="#F47C20"/>
                <path d="M32 36C36.4183 36 40 32.4183 40 28C40 23.5817 36.4183 20 32 20" stroke="#0B2A5A" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Professional Services</h3>
          </a>
        </div>

        <!-- Managed Services -->
        <div class="app-card">
          <a href="services/managed.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M32 20C25.3726 20 20 25.3726 20 32C20 38.6274 25.3726 44 32 44C38.6274 44 44 38.6274 44 32" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
                <path d="M32 28L36 32L32 36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M24 32H36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Managed Services</h3>
          </a>
        </div>

        <!-- Big Data Analytics -->
        <div class="app-card">
          <a href="services/bigdata.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <rect x="20" y="20" width="24" height="24" rx="2" fill="#0B2A5A"/>
                <rect x="24" y="36" width="4" height="4" fill="#F47C20"/>
                <rect x="30" y="32" width="4" height="8" fill="#F47C20"/>
                <rect x="36" y="28" width="4" height="12" fill="#F47C20"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Big Data Analytics</h3>
          </a>
        </div>

        <!-- Cloud Services -->
        <div class="app-card">
          <a href="services/cloud.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M44 36C46.2091 36 48 34.2091 48 32C48 29.7909 46.2091 28 44 28C43.8178 28 43.6382 28.0116 43.4615 28.0342C42.7523 24.1101 39.3979 21.1436 35.2222 21.0038C31.7211 20.8866 28.5486 22.7881 27.0614 25.6492C26.7708 25.5563 26.4642 25.5 26.1481 25.5C24.4097 25.5 23 26.8431 23 28.5C23 28.6929 23.0176 28.8821 23.0515 29.0661C20.7823 29.6402 19 31.6543 19 34C19 36.7614 21.2386 39 24 39H44Z" fill="#0B2A5A"/>
                <path d="M34 39L34 45" stroke="#F47C20" stroke-width="3" stroke-linecap="round"/>
                <path d="M30 43L34 47L38 43" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Cloud Services</h3>
          </a>
        </div>

        <!-- Security Services -->
        <div class="app-card">
          <a href="services/security.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M32 20L20 24V32C20 39.732 25.36 47.036 32 48C38.64 47.036 44 39.732 44 32V24L32 20Z" fill="#0B2A5A"/>
                <path d="M28 32L30.5 34.5L36 29" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Security Services</h3>
          </a>
        </div>

        <!-- Customer Experience -->
        <div class="app-card">
          <a href="services/customer-experience.html" style="text-decoration: none; color: inherit; text-align: center;">
            <div class="app-icon" style="margin: 0 auto;">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
                <path d="M44 32C44 25.3726 38.6274 20 32 20C25.3726 20 20 25.3726 20 32C20 38.6274 25.3726 44 32 44" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
                <path d="M32 28V32L36 36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="44" cy="44" r="4" fill="#F47C20"/>
              </svg>
            </div>
            <h3 class="app-title" style="text-align: center;">Customer Experience</h3>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Product Section -->
  <section id="products" class="section product">
    <div class="container">
      <h2 class="section-title">Our Products</h2>
      <p class="section-subtitle">We provide high-quality products and services for you<br>We make solutions that meet your business needs</p>

      <!-- Products Grid -->
      <div class="app-grid products-grid">
        <!-- AgnoCon -->
        <div class="app-card product-card active" data-product="agnocon">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M20 32C20 25.3726 25.3726 20 32 20C38.6274 20 44 25.3726 44 32C44 38.6274 38.6274 44 32 44C25.3726 44 20 38.6274 20 32Z" fill="#F47C20"/>
              <path d="M32 28V36" stroke="white" stroke-width="3" stroke-linecap="round"/>
              <path d="M28 32H36" stroke="white" stroke-width="3" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">AgnoCon</h3>
        </div>

        <!-- Click2Go -->
        <div class="app-card product-card" data-product="click2go">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M20 32C20 25.3726 25.3726 20 32 20C38.6274 20 44 25.3726 44 32C44 38.6274 38.6274 44 32 44" stroke="#0B2A5A" stroke-width="3" stroke-linecap="round"/>
              <path d="M32 28L36 32L32 36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M24 32H36" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">Click2Go</h3>
        </div>

        <!-- AgnoVideo -->
        <div class="app-card product-card" data-product="agnovideo">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="18" y="22" width="28" height="20" rx="2" fill="#0B2A5A"/>
              <path d="M30 28L36 32L30 36V28Z" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">AgnoVideo</h3>
        </div>

        <!-- Knowledge Base -->
        <div class="app-card product-card" data-product="knowledgebase">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M22 20H42C43.1046 20 44 20.8954 44 22V42C44 43.1046 43.1046 44 42 44H22C20.8954 44 20 43.1046 20 42V22C20 20.8954 20.8954 20 22 20Z" fill="#0B2A5A"/>
              <path d="M26 28H38" stroke="#F47C20" stroke-width="2" stroke-linecap="round"/>
              <path d="M26 32H38" stroke="#F47C20" stroke-width="2" stroke-linecap="round"/>
              <path d="M26 36H34" stroke="#F47C20" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">Knowledge Base</h3>
        </div>

        <!-- Survey Assist -->
        <div class="app-card product-card" data-product="surveyassist">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="16" y="14" width="32" height="36" rx="2" fill="#0B2A5A"/>
              <rect x="20" y="18" width="24" height="4" rx="1" fill="#FFFFFF"/>
              <rect x="20" y="26" width="16" height="3" rx="1" fill="#FFFFFF"/>
              <rect x="20" y="33" width="20" height="3" rx="1" fill="#FFFFFF"/>
              <rect x="20" y="40" width="12" height="3" rx="1" fill="#FFFFFF"/>
              <circle cx="40" cy="27" r="2" fill="#F47C20"/>
              <circle cx="40" cy="34" r="2" fill="#F47C20"/>
              <path d="M38 40L40 42L44 38" stroke="#F47C20" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">Survey Assist</h3>
        </div>

        <!-- AgKnow -->
        <div class="app-card product-card" data-product="agknow">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M32 20L20 24V32C20 39.732 25.36 47.036 32 48C38.64 47.036 44 39.732 44 32V24L32 20Z" fill="#0B2A5A"/>
              <path d="M28 32L30.5 34.5L36 29" stroke="#F47C20" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="app-title">AgKnow</h3>
        </div>

        <!-- Agent Scripting -->
        <div class="app-card product-card" data-product="agentscripting">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M20 24H44V40C44 41.1046 43.1046 42 42 42H22C20.8954 42 20 41.1046 20 40V24Z" fill="#0B2A5A"/>
              <path d="M24 20H40V24H24V20Z" fill="#F47C20"/>
              <path d="M26 30H38" stroke="white" stroke-width="2" stroke-linecap="round"/>
              <path d="M26 34H34" stroke="white" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">Agent Scripting</h3>
        </div>

        <!-- Outbound Dialer -->
        <div class="app-card product-card" data-product="outbounddialer">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M40 24V40L36 36H24C22.8954 36 22 35.1046 22 34V24C22 22.8954 22.8954 22 24 22H38C39.1046 22 40 22.8954 40 24Z" fill="#0B2A5A"/>
              <circle cx="42" cy="42" r="6" fill="#F47C20"/>
              <path d="M42 39V45" stroke="white" stroke-width="2" stroke-linecap="round"/>
              <path d="M39 42H45" stroke="white" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <h3 class="app-title">Outbound Dialer</h3>
        </div>

        <!-- Wall Board -->
        <div class="app-card product-card" data-product="wallboard">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <rect x="20" y="20" width="24" height="24" rx="2" fill="#0B2A5A"/>
              <rect x="24" y="24" width="7" height="7" rx="1" fill="#F47C20"/>
              <rect x="33" y="24" width="7" height="7" rx="1" fill="#F47C20"/>
              <rect x="24" y="33" width="7" height="7" rx="1" fill="#F47C20"/>
              <rect x="33" y="33" width="7" height="7" rx="1" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">Wall Board</h3>
        </div>

        <!-- WhatsApp Campaign Solutions -->
        <div class="app-card product-card" data-product="whatsappcampaign">
          <div class="app-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="32" cy="32" r="32" fill="#F5F5F5"/>
              <path d="M32 20C25.3726 20 20 25.3726 20 32C20 34.5829 20.8429 36.9832 22.2676 39.0058L20.6582 44.0195L25.8692 42.4915C27.7799 43.7522 30.0706 44.5 32.5 44.5C39.1274 44.5 44.5 39.1274 44.5 32.5C44.5 25.8726 39.1274 20.5 32.5 20.5" fill="#0B2A5A"/>
              <path d="M36 30C36 28.8954 35.1046 28 34 28H30C28.8954 28 28 28.8954 28 30V34C28 35.1046 28.8954 36 30 36H34L36 38V30Z" fill="#F47C20"/>
            </svg>
          </div>
          <h3 class="app-title">WhatsApp Campaign</h3>
        </div>
      </div>

      <!-- Product Description -->
      <div class="section-description">
        <p>Click on any product to view its details.</p>
      </div>
    </div>
  </section>

  <!-- Certification Section -->
  <section class="section">
    <div class="container">
      <h2 class="section-title">AgnoShin is Great Place to Work Certified</h2>
      <p class="section-subtitle">For the second year in a row</p>

      <div class="about-content">
        <div class="about-image">
          <img src="media/Great place to work.jpeg" alt="Great Place to Work Certification">
        </div>

        <div class="about-text">
          <p>AgnoShin Technologies has been certified Great Place to Work India for the consecutive second year from August 2023 to August 2024.</p>

          <p>We are proud to have created a high-trust and high-performance culture in AgnoShin. You can view AgnoShin Technologies on the Great Place to Work website in mid-size organizations.</p>

          <p>AgnoShin Technologies was evaluated under five categories:</p>
          <ul style="list-style-position: inside; margin-bottom: 20px;">
            <li>Credibility</li>
            <li>Respect</li>
            <li>Fairness</li>
            <li>Pride</li>
            <li>Camaraderie</li>
          </ul>

          <p>For any organization to be eligible for the Great Place to Work certification, 70 percent of the employees must respond and rate the organization. As a mid-size organization with 150 employees, we had a great response rate of 96 percent.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 40px; align-items: start;">
        <!-- Company Info Column -->
        <div class="footer-column">
          <img src="media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="footer-logo" style="height: 150px; margin-bottom: 25px; width: auto;">
          <p class="footer-description" style="font-size: 14px; line-height: 1.6; color: #555; margin: 0;">
            AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.
          </p>
        </div>

        <!-- Quick Links Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Quick Links</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="#" style="color: #555; text-decoration: none; font-size: 14px;">Home</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="#about" style="color: #555; text-decoration: none; font-size: 14px;">About Us</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="#services" style="color: #555; text-decoration: none; font-size: 14px;">Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="#products" style="color: #555; text-decoration: none; font-size: 14px;">Products</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="#partners" style="color: #555; text-decoration: none; font-size: 14px;">Clients</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="industries.html" style="color: #555; text-decoration: none; font-size: 14px;">Industries</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="csr.html" style="color: #555; text-decoration: none; font-size: 14px;">CSR</a></li>
          </ul>
        </div>

        <!-- Services Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Services</h3>
          <ul class="footer-links">
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/professional.html" style="color: #555; text-decoration: none; font-size: 14px;">Professional Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/managed.html" style="color: #555; text-decoration: none; font-size: 14px;">Managed Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/bigdata.html" style="color: #555; text-decoration: none; font-size: 14px;">Big Data Analytics</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/cloud.html" style="color: #555; text-decoration: none; font-size: 14px;">Cloud Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/security.html" style="color: #555; text-decoration: none; font-size: 14px;">Security Services</a></li>
            <li class="footer-link" style="margin-bottom: 8px;"><a href="services/customer-experience.html" style="color: #555; text-decoration: none; font-size: 14px;">Customer Experience</a></li>
          </ul>
        </div>

        <!-- Contact Us Column -->
        <div class="footer-column">
          <h3 class="footer-title" style="color: #F47C20; font-size: 16px; margin-bottom: 15px; font-weight: 600;">Contact Us</h3>
          <div class="footer-contact-info">
            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📞 Call us:</strong><br>
                <a href="tel:+************" style="color: #555; text-decoration: none;">+91 9551818896</a>
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Corporate Office:</strong><br>
                UAE & India & Africa
              </p>
            </div>

            <div class="footer-contact-item" style="margin-bottom: 12px;">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">📍 Our Operating Continents:</strong><br>
                US, Asia Pacific, Middle East & Africa
              </p>
            </div>

            <div class="footer-contact-item">
              <p style="color: #555; font-size: 14px; line-height: 1.5; margin: 0;">
                <strong style="color: #F47C20;">✉️ Mail to:</strong><br>
                <a href="mailto:<EMAIL>" style="color: #555; text-decoration: none;"><EMAIL></a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="copyright">
          © 2025 AgnoShin Technologies. All rights reserved.
        </div>

        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="advanced_interactions.js"></script>
</body>
</html>
