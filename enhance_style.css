/* Odoo-style CSS for AgnoShin Website */
:root {
  /* Brand Color Palette */
  --primary-dark-blue: #073763;
  --light-blue: #0B2A5A;
  --bright-orange: #F47C20;
  --white: #FFFFFF;
  --medium-gray: #6E7C8E;
  --dark-gray: #2E2E2E;
  --light-gray: #F5F5F5;
  --card-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  --hover-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  --font-heading: 'Poppins', var(--font-primary);

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  color: var(--dark-gray);
  background-color: var(--light-gray);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  color: var(--primary-dark-blue);
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.2s ease;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.footer .container {
  padding: 0 var(--spacing-lg);
}

.header .container {
  padding: 0 var(--spacing-xl);
  max-width: 1440px;
}

/* Header styles */
.header {
  background-color: var(--white);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0;
  border-bottom: 1px solid #eee;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  height: 60px;
  overflow: visible;
}

.logo {
  height: 180px;
  margin-top: -60px;
  margin-bottom: -60px;
}

.logo-link {
  display: block;
  overflow: visible;
  position: relative;
  z-index: 10;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 24px;
  justify-content: center;
}

.nav-link {
  color: var(--dark-gray);
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 14px;
  text-transform: uppercase;
}

.nav-link:hover {
  color: #F3913E;
}

.header-actions {
  display: flex;
  align-items: center;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary {
  background-color: #F3913E;
  color: var(--white);
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 8px 16px;
  font-size: 14px;
}

.btn-primary:hover {
  background-color: #e06a10;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-dark-blue);
  border: 1px solid var(--primary-dark-blue);
}

.btn-secondary:hover {
  background-color: rgba(7, 55, 99, 0.05);
}

/* App grid layout */
.app-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xxl) 0;
}

.app-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.app-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--hover-shadow);
}

.app-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dark-gray);
  margin-top: var(--spacing-sm);
  text-align: center;
}

/* Section styles */
.section {
  padding: var(--spacing-xxl) 0;
}

.section-title {
  font-size: 28px;
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-subtitle {
  text-align: center;
  color: var(--medium-gray);
  max-width: 700px;
  margin: 0 auto var(--spacing-xl);
}

/* Hero section */
.hero {
  background-color: var(--white);
  padding: var(--spacing-xxl) 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero-title {
  font-size: 42px;
  margin-bottom: var(--spacing-lg);
}

.hero-subtitle {
  font-size: 18px;
  color: var(--medium-gray);
  margin-bottom: var(--spacing-xl);
}

/* About section */
.about {
  background-color: var(--white);
}

.about-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xxl);
}

.about-image {
  flex: 1;
}

.about-image img {
  width: 100%;
  border-radius: var(--radius-md);
  box-shadow: var(--card-shadow);
}

.about-text {
  flex: 1;
}

/* Partners/Clients section */
.partners {
  background-color: var(--white);
  overflow: hidden;
}

/* Marquee Container */
.marquee-container {
  width: 100%;
  overflow: hidden;
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
  background: linear-gradient(90deg,
    rgba(255,255,255,1) 0%,
    rgba(255,255,255,0) 10%,
    rgba(255,255,255,0) 90%,
    rgba(255,255,255,1) 100%);
}

.marquee-content {
  display: flex;
  align-items: center;
  animation: marquee 46s linear infinite;
  gap: var(--spacing-xxl);
  width: max-content;
}

.client-logo {
  height: 60px;
  width: auto;
  flex-shrink: 0;
  filter: none;
  opacity: 1;
  transition: all 0.3s ease;
  object-fit: contain;
}

.client-logo:hover {
  transform: scale(1.1);
  opacity: 1;
}

/* Marquee Animation - Seamless Loop */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Pause animation on hover */
.marquee-container:hover .marquee-content {
  animation-play-state: paused;
}

/* Legacy partner logos styles for backward compatibility */
.partner-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.partner-logo {
  height: 60px;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.partner-logo:hover {
  filter: grayscale(0%);
  opacity: 1;
}

/* Product section */
.product {
  background-color: var(--white);
}

/* Products Grid */
.products-grid {
  grid-template-columns: repeat(5, 1fr);
}

.product-card {
  position: relative;
  cursor: pointer;
  transition: all 0.25s ease;
}

.product-card.active {
  background-color: rgba(244, 124, 32, 0.1);
  transform: translateY(-5px);
  box-shadow: var(--hover-shadow);
}

.product-card.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(244, 124, 32, 0.1);
}

.section-description {
  margin-top: var(--spacing-xl);
  text-align: center;
  color: var(--medium-gray);
  font-style: italic;
}

.product-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xxl);
}

.product-image {
  flex: 1;
}

.product-text {
  flex: 1;
}

.features {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature {
  flex: 1;
  text-align: center;
}

.feature-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-md);
}

/* Footer */
.footer {
  background-color: var(--white);
  padding: 40px 0 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 40px;
  align-items: start;
  margin-bottom: 30px;
}

.footer-left {
  max-width: 250px;
  margin-right: 30px;
}

.footer-right {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.footer-column {
  padding-right: 0;
}

.footer-logo {
  height: 150px;
  width: auto;
  display: block;
  margin-bottom: 25px;
}

.footer-description {
  font-size: 14px;
  line-height: 1.5;
  color: #555;
  margin: 0;
  max-width: 250px;
}

.footer-title {
  font-size: 15px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link {
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.footer-link a {
  color: #555;
  text-decoration: none;
}

.footer-link a:hover {
  color: var(--primary-dark-blue);
}

.footer-link i {
  margin-right: 5px;
}

.footer-bottom {
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
}

.copyright {
  color: #777;
  font-size: 13px;
}

.social-links {
  display: flex;
  gap: 10px;
}

.social-link {
  color: #777;
  font-size: 13px;
}

.social-link:hover {
  color: var(--primary-dark-blue);
}

/* View all apps button */
.view-all {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
}

.view-all-link {
  display: flex;
  align-items: center;
  color: var(--primary-dark-blue);
  font-weight: 500;
}

.view-all-link svg {
  margin-left: var(--spacing-sm);
}

.view-all-link:hover {
  color: var(--bright-orange);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .app-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .app-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .about-content, .product-content {
    flex-direction: column;
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-menu {
    display: none;
  }

  .hero-title {
    font-size: 32px;
  }

  .features {
    flex-wrap: wrap;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 25px;
  }

  .footer {
    padding: 30px 0 15px;
  }
}

@media (max-width: 576px) {
  .app-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .features {
    flex-direction: column;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
}
