/* Additional CSS for enhanced UI/UX with tooltips and animations */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in-visible {
  opacity: 1;
  transform: translateY(0);
}

.app-tooltip {
  position: absolute;
  bottom: -15px;
  left: 0;
  right: 0;
  background-color: rgba(7, 55, 99, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  font-size: 12px;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.2s ease, transform 0.2s ease; /* Faster transitions */
  z-index: 5;
  pointer-events: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  margin: 0 auto;
  text-align: center;
}

.app-card {
  position: relative;
  z-index: 1;
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1); /* Faster transition */
}

.app-icon {
  transition: transform 0.2s ease; /* Faster transition */
}

.header-scrolled {
  padding: 10px 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.header {
  transition: transform 0.3s ease, padding 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

.btn {
  position: relative;
  overflow: hidden;
}

.nav-link.active {
  color: #F3913E;
  font-weight: 700;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--dark-gray);
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-10px);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
  }
}

/* Enhanced app grid with staggered animation */
.app-grid .app-card:nth-child(1) { transition-delay: 0.05s; }
.app-grid .app-card:nth-child(2) { transition-delay: 0.1s; }
.app-grid .app-card:nth-child(3) { transition-delay: 0.15s; }
.app-grid .app-card:nth-child(4) { transition-delay: 0.2s; }
.app-grid .app-card:nth-child(5) { transition-delay: 0.25s; }
.app-grid .app-card:nth-child(6) { transition-delay: 0.3s; }
.app-grid .app-card:nth-child(7) { transition-delay: 0.35s; }
.app-grid .app-card:nth-child(8) { transition-delay: 0.4s; }
.app-grid .app-card:nth-child(9) { transition-delay: 0.45s; }
.app-grid .app-card:nth-child(10) { transition-delay: 0.5s; }
.app-grid .app-card:nth-child(11) { transition-delay: 0.55s; }
.app-grid .app-card:nth-child(12) { transition-delay: 0.6s; }

/* Pulse animation for CTA buttons */
.btn-primary {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 124, 32, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 124, 32, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 124, 32, 0);
  }
}

/* Enhanced hero section */
.hero {
  position: relative;
  overflow: hidden;
  background-color: var(--primary-dark-blue);
  color: white;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(7, 55, 99, 0.9) 0%, rgba(11, 42, 90, 0.9) 100%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  color: white;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(90deg, var(--bright-orange), #ff9f50);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Floating animation for feature icons */
.feature-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Static text for prominent features */
.features-title, .feature-item h3 {
  animation: none;
  transform: none;
}

/* Enhanced section titles */
.section-title {
  position: relative;
  padding-bottom: 15px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--bright-orange);
  border-radius: 3px;
}

/* Product Cards */
.product-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-card.active {
  border: 2px solid var(--bright-orange);
}

.product-details {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
