<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Scripting - Enhance Agent Efficiency and Customer Experience</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="../enhance_style.css">
  <link rel="stylesheet" href="../enhanced_ui.css">

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    .product-detail-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
      background-color: #fff;
    }

    .product-header {
      margin-bottom: 30px;
    }

    .product-content {
      display: flex;
      gap: 40px;
      margin-top: 30px;
    }

    .product-description {
      flex: 1;
    }

    .product-image {
      flex: 1;
    }

    .features-container {
      margin-top: 40px;
    }

    .features-title {
      color: #073763;
      margin-bottom: 20px;
    }

    .features-section {
      background-color: #fff;
      padding: 40px 20px;
      margin-top: 30px;
      text-align: center;
    }

    .features-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .feature-item {
      flex: 0 0 calc(25% - 30px);
      text-align: center;
      margin-bottom: 30px;
      transition: transform 0.3s ease;
      padding: 20px 15px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-bottom: 3px solid transparent;
    }

    /* Animation for feature icons */
    .feature-icon {
      animation: float 3s ease-in-out infinite;
    }

    .feature-item:nth-child(odd) .feature-icon {
      animation-delay: 0.5s;
    }

    .feature-item:nth-child(3n) .feature-icon {
      animation-delay: 1s;
    }

    .feature-item:nth-child(3n+1) .feature-icon {
      animation-delay: 1.5s;
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-5px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    .feature-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(244, 124, 32, 0.2);
      border-bottom: 3px solid #F47C20;
    }

    .feature-item:hover .feature-icon {
      animation-play-state: paused;
      transform: translateY(-5px) scale(1.05);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 50%;
      color: #F47C20;
      font-size: 32px;
      border: 2px solid #F47C20;
    }

    .feature-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #F47C20;
    }

    .feature-description {
      font-size: 14px;
      color: #6E7C8E;
    }

    .feature-showcase {
      display: flex;
      flex-direction: column;
      gap: 60px;
      margin-top: 40px;
    }

    .feature-showcase-item {
      display: flex;
      gap: 40px;
      align-items: center;
      background-color: #fff;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
      padding: 0;
    }

    .feature-showcase-item:nth-child(even) {
      flex-direction: row-reverse;
    }

    .feature-showcase-content {
      flex: 1;
      padding: 40px;
    }

    .feature-showcase-image {
      flex: 1;
      min-height: 300px;
      background-size: cover;
      background-position: center;
      position: relative;
      border-radius: 0;
      overflow: hidden;
      box-shadow: none;
    }

    .feature-showcase-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    /* Dashboard Demo Section */
    .dashboard-demo-section {
      background: linear-gradient(135deg, #0B2A5A 0%, #073763 100%);
      color: white;
      padding: 60px 40px;
      border-radius: 12px;
      margin: 50px 0;
      position: relative;
      overflow: hidden;
    }

    .dashboard-demo-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
      background-size: cover;
      background-position: center;
      opacity: 0.15;
      z-index: 0;
    }

    .dashboard-demo-content {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 40px;
    }

    .dashboard-demo-text {
      flex: 1;
    }

    .dashboard-demo-image {
      flex: 1;
      text-align: center;
    }

    .dashboard-demo-image img {
      max-width: 100%;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border: 3px solid rgba(255, 255, 255, 0.1);
    }

    .dashboard-demo-title {
      color: white;
      font-size: 28px;
      margin-bottom: 20px;
    }

    .dashboard-demo-description {
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.6;
      margin-bottom: 25px;
    }

    .benefits-section {
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      padding: 60px 20px;
      margin-top: 60px;
      border-radius: 8px;
    }

    .benefits-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .benefits-title {
      text-align: center;
      margin-bottom: 40px;
      color: #073763;
    }

    .benefits-grid {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    .benefit-item {
      display: flex;
      gap: 20px;
      align-items: flex-start;
      background-color: #fff;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #F47C20;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .benefit-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .benefit-icon {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(244, 124, 32, 0.1);
      border-radius: 50%;
      color: #F47C20;
      font-size: 20px;
      flex-shrink: 0;
    }

    .benefit-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #F47C20;
    }

    .back-button {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      color: #073763;
      font-weight: 500;
      margin-bottom: 20px;
      text-decoration: none;
    }

    .back-button:hover {
      text-decoration: underline;
    }

    @media (max-width: 992px) {
      .product-content {
        flex-direction: column;
      }

      .dashboard-demo-content {
        flex-direction: column;
      }

      .feature-showcase-item,
      .feature-showcase-item:nth-child(even) {
        flex-direction: column;
      }

      .feature-item {
        flex: 0 0 calc(50% - 30px);
      }
    }

    @media (max-width: 768px) {
      .feature-item {
        flex: 0 0 calc(50% - 30px);
      }

      .feature-showcase-item {
        flex-direction: column;
      }

      .feature-showcase-image {
        min-height: 200px;
      }
    }

    @media (max-width: 576px) {
      .feature-item {
        flex: 0 0 100%;
      }

      .benefit-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .benefit-icon {
        margin-bottom: 15px;
      }

      /* Mobile adjustments for the header with centered logo */
      .product-detail-container > div:first-child {
        flex-direction: column;
        gap: 20px;
        margin-bottom: 40px;
      }

      .product-detail-container > div:first-child > div:first-child {
        position: static;
        margin-top: 10px;
      }

      .product-detail-container > div:first-child > div:nth-child(2) {
        order: -1;
      }
    }
  </style>
</head>
<body>
  <!-- Header Navigation -->
  <header class="header">
    <div class="container header-container">
      <a href="../enhance_agnoshin.html" class="logo-link">
        <img src="../media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="logo">
      </a>

      <nav>
        <ul class="nav-menu">
          <li><a href="../enhance_agnoshin.html" class="nav-link">HOME</a></li>
          <li><a href="../enhance_agnoshin.html#about" class="nav-link">ABOUT US</a></li>
          <li><a href="../enhance_agnoshin.html#services" class="nav-link">SERVICES</a></li>
          <li><a href="../enhance_agnoshin.html#products" class="nav-link active">OUR PRODUCTS</a></li>
          <li><a href="../industries.html" class="nav-link">INDUSTRIES</a></li>
          <li><a href="../enhance_agnoshin.html#partners" class="nav-link">OUR CLIENTS</a></li>
          <li><a href="#" class="nav-link">CSR</a></li>
        </ul>
      </nav>

      <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
      </button>

      <div class="header-actions">
        <a href="#" class="btn btn-primary">CONTACT US</a>
      </div>
    </div>
  </header>

  <!-- Product Detail Section -->
  <section class="section">
    <div class="product-detail-container">
      <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 20px; position: relative;">
        <div style="position: absolute; left: 0;">
          <a href="../enhance_agnoshin.html#products" class="back-button">
            <i class="fas fa-arrow-left"></i> Back to Products
          </a>
        </div>

        <div style="display: flex; align-items: center; gap: 15px; justify-content: center;">
          <img src="../media/agent-desktop.svg" alt="Agent Scripting Logo" style="height: 60px; margin-bottom: 20px;">
          <h1 style="margin-bottom: 20px; color: #F47C20;">Agent Scripting</h1>
        </div>
      </div>

      <div class="product-header" style="text-align: center; margin-top: 30px;">
        <h2>Enhance Agent <span style="color: #F47C20;">Efficiency</span> and Customer Experience</h2>
      </div>

      <div class="product-content">
        <div class="product-description" style="text-align: left;">
          <p>The Agent Scripting Application (ASA) is a powerful tool designed to enhance the efficiency and effectiveness of agents in various operational environments, such as customer support, sales, and technical assistance. This application empowers agents to create, manage, and execute scripts that guide interactions with customers, ensuring consistent communication and improved service delivery.</p>
          <p>ASA offers a range of customizable templates tailored to specific scenarios, enabling agents to quickly adapt scripts to meet unique customer needs. ASA seamlessly integrates with CRM and other operational tools, enabling agents to access customer information and history without switching applications.</p>
          <p>The application supports various communication channels, including phone, chat, and email, ensuring a cohesive experience across platforms. By leveraging these features, the ASA significantly enhances agent productivity and contributes to a higher level of customer satisfaction.</p>
        </div>
        <div class="product-image">
          <!-- Keywords for image generation: customer service agent, headset, computer screen, call center, script interface, professional -->
          <img src="../media/agent_scripting_main.jpeg" alt="Agent Scripting Interface" style="width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
        </div>
      </div>

      <!-- Dashboard Demo Section -->
      <div class="dashboard-demo-section">
        <div class="dashboard-demo-content">
          <div class="dashboard-demo-text" style="text-align: left;">
            <h2 class="dashboard-demo-title">Streamlined Agent Experience</h2>
            <p class="dashboard-demo-description">Agent Scripting transforms how your contact center agents interact with customers. With its intuitive interface and powerful customization capabilities, agents can follow optimized conversation flows that adapt in real-time based on customer responses.</p>
            <p class="dashboard-demo-description">The system seamlessly integrates with your existing CRM and knowledge base tools, creating a unified agent desktop that enhances productivity and ensures consistent information delivery across all customer interactions.</p>
          </div>
          <div class="dashboard-demo-image">
            <!-- Keywords for image generation: agent scripting dashboard, call center interface, conversation flow, professional UI -->
            <img src="../media/agent_scripting_dashboard.jpeg" alt="Agent Scripting Dashboard Preview">
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features-section">
    <div class="container">
      <h2 class="section-title"><span style="color: #F47C20;">Key</span> Features</h2>
      <div class="features-grid">
        <!-- Script Editor -->
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-edit"></i>
          </div>
          <h3 class="feature-title">Script Editor</h3>
          <p class="feature-description">Intuitive drag-and-drop interface for creating and editing scripts with conditional logic, branching, and dynamic content.</p>
        </div>

        <!-- Testing and Debugging -->
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-bug"></i>
          </div>
          <h3 class="feature-title">Testing Tools</h3>
          <p class="feature-description">Comprehensive testing and debugging tools to ensure scripts function correctly before deployment.</p>
        </div>

        <!-- Version Control -->
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-code-branch"></i>
          </div>
          <h3 class="feature-title">Version Control</h3>
          <p class="feature-description">Robust version control system allowing teams to track changes, revert to previous versions, and collaborate effectively.</p>
        </div>

        <!-- Script Libraries -->
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-book"></i>
          </div>
          <h3 class="feature-title">Script Libraries</h3>
          <p class="feature-description">Extensive library of templates and reusable components to accelerate script creation and ensure consistency.</p>
        </div>
      </div>

      <div class="feature-showcase">
        <div class="feature-showcase-item">
          <div class="feature-showcase-content" style="text-align: left;">
            <h3 style="color: #F47C20; font-size: 24px; margin-bottom: 15px;">Intuitive Script Editor</h3>
            <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">The Agent Scripting Application features a powerful yet user-friendly script editor that enables agents and administrators to create sophisticated interaction flows without requiring technical expertise.</p>
            <p style="color: #333; line-height: 1.6;">The editor supports conditional logic, allowing scripts to adapt based on customer responses, and includes a variety of pre-built components for common scenarios such as authentication, troubleshooting, and sales processes.</p>
          </div>
          <div class="feature-showcase-image">
            <!-- Keywords for image generation: script editor interface, flowchart, decision tree, drag and drop, user interface -->
            <img src="../media/script_editor.jpeg" alt="Script Editor Interface" style="width: 100%; height: auto;">
          </div>
        </div>

        <div class="feature-showcase-item">
          <div class="feature-showcase-content" style="text-align: left;">
            <h3 style="color: #F47C20; font-size: 24px; margin-bottom: 15px;">Testing Tools</h3>
            <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">Comprehensive testing and debugging tools to ensure scripts function correctly before deployment.</p>
            <p style="color: #333; line-height: 1.6;">These tools allow teams to simulate customer interactions, identify potential issues, and optimize script performance, ensuring a smooth experience for both agents and customers.</p>
          </div>
          <div class="feature-showcase-image">
            <img src="../media/testing_tools.jpeg" alt="Testing Tools" style="width: 100%; height: auto;">
          </div>
        </div>

        <div class="feature-showcase-item">
          <div class="feature-showcase-content" style="text-align: left;">
            <h3 style="color: #F47C20; font-size: 24px; margin-bottom: 15px;">Version Control</h3>
            <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">Robust version control system allowing teams to track changes, revert to previous versions, and collaborate effectively.</p>
            <p style="color: #333; line-height: 1.6;">This system maintains a complete history of script modifications, enabling teams to understand how scripts have evolved over time and ensuring that no valuable work is ever lost.</p>
          </div>
          <div class="feature-showcase-image">
            <img src="../media/version_control.jpeg" alt="Version Control" style="width: 100%; height: auto;">
          </div>
        </div>

        <div class="feature-showcase-item">
          <div class="feature-showcase-content" style="text-align: left;">
            <h3 style="color: #F47C20; font-size: 24px; margin-bottom: 15px;">Script Libraries</h3>
            <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">Extensive library of templates and reusable components to accelerate script creation and ensure consistency.</p>
            <p style="color: #333; line-height: 1.6;">These pre-built elements can be easily customized and incorporated into new scripts, reducing development time and maintaining a consistent experience across different customer interactions.</p>
          </div>
          <div class="feature-showcase-image">
            <img src="../media/script_libraries.jpeg" alt="Script Libraries" style="width: 100%; height: auto;">
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="benefits-section">
    <div class="benefits-container">
      <h2 class="benefits-title">Key <span style="color: #F47C20;">Benefits</span></h2>

      <div class="benefits-grid">
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="benefit-content" style="text-align: left;">
            <h3 class="benefit-title">Standardization</h3>
            <p style="color: #444; line-height: 1.6;">Ensures that all agents deliver consistent information and adhere to company policies, resulting in a uniform customer experience regardless of which agent handles the interaction.</p>
          </div>
        </div>

        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <div class="benefit-content" style="text-align: left;">
            <h3 class="benefit-title">Efficiency</h3>
            <p style="color: #444; line-height: 1.6;">Reduces response times and helps agents navigate complex inquiries with confidence, leading to shorter handling times and increased productivity.</p>
          </div>
        </div>

        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <div class="benefit-content" style="text-align: left;">
            <h3 class="benefit-title">Training Support</h3>
            <p style="color: #444; line-height: 1.6;">Serves as a vital resource for new agents, enabling them to learn and adapt quickly while providing consistent service quality during the onboarding period.</p>
          </div>
        </div>

        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="benefit-content" style="text-align: left;">
            <h3 class="benefit-title">Performance Improvement</h3>
            <p style="color: #444; line-height: 1.6;">Facilitates the collection of data on agent performance, allowing for ongoing training and refinement of scripts to continuously improve customer interactions.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container footer-container">
      <div class="footer-content">
        <div>
          <img src="https://agnoshin.com/wp-content/uploads/2023/08/agnoshin-logo.png" alt="AgnoShin Logo" class="footer-logo">
          <p class="footer-description">AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.</p>
        </div>

        <div>
          <h3 class="footer-title">Quick Links</h3>
          <ul class="footer-links">
            <li class="footer-link"><a href="../enhance_agnoshin.html">Home</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#about">About Us</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#services">Services</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#products">Products</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#partners">Clients</a></li>
          </ul>
        </div>

        <div>
          <h3 class="footer-title">Contact Us</h3>
          <ul class="footer-contact">
            <li class="footer-contact-item"><i class="fas fa-map-marker-alt"></i> 123 Business Avenue, Tech City</li>
            <li class="footer-contact-item"><i class="fas fa-phone"></i> +****************</li>
            <li class="footer-contact-item"><i class="fas fa-envelope"></i> <EMAIL></li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="copyright">
          © 2025 AgnoShin Technologies. All rights reserved.
        </div>

        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="../advanced_interactions.js"></script>
</body>
</html>

