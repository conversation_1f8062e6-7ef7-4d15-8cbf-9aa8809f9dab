<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wall Board - Real-Time Performance Visualization</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="../enhance_style.css">
  <link rel="stylesheet" href="../enhanced_ui.css">

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    .product-detail-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
      background-color: #fff;
    }

    .product-header {
      margin-bottom: 30px;
    }

    .product-content {
      display: flex;
      gap: 40px;
      margin-top: 30px;
    }

    .product-description {
      flex: 1;
    }

    .product-image {
      flex: 1;
    }

    .features-container {
      margin-top: 40px;
    }

    .features-title {
      color: #073763;
      margin-bottom: 20px;
    }

    .features-section {
      background-color: #fff;
      padding: 40px 20px;
      margin-top: 30px;
      text-align: center;
    }

    .features-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .feature-item {
      flex: 0 0 calc(25% - 30px);
      text-align: center;
      margin-bottom: 30px;
      transition: transform 0.3s ease;
      padding: 20px 15px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-bottom: 3px solid transparent;
    }

    /* Animation for feature icons */
    .feature-icon {
      animation: float 3s ease-in-out infinite;
    }

    .feature-item:nth-child(odd) .feature-icon {
      animation-delay: 0.5s;
    }

    .feature-item:nth-child(3n) .feature-icon {
      animation-delay: 1s;
    }

    .feature-item:nth-child(3n+1) .feature-icon {
      animation-delay: 1.5s;
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-5px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    .feature-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(244, 124, 32, 0.2);
      border-bottom: 3px solid #F47C20;
    }

    .feature-item:hover .feature-icon {
      animation-play-state: paused;
      transform: translateY(-5px) scale(1.05);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 50%;
      color: #F47C20;
      font-size: 32px;
      border: 2px solid #F47C20;
    }

    .feature-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #F47C20;
    }

    .feature-description {
      font-size: 14px;
      color: #6E7C8E;
    }

    /* Enhanced styles for Wall Board */
    .dashboard-demo-section {
      background: linear-gradient(135deg, #0B2A5A 0%, #073763 100%);
      color: white;
      padding: 60px 40px;
      border-radius: 12px;
      margin: 50px 0;
      position: relative;
      overflow: hidden;
    }

    .dashboard-demo-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
      background-size: cover;
      background-position: center;
      opacity: 0.15;
      z-index: 0;
    }

    .dashboard-demo-content {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 40px;
    }

    .dashboard-demo-text {
      flex: 1;
    }

    .dashboard-demo-image {
      flex: 1;
      text-align: center;
    }

    .dashboard-demo-image img {
      max-width: 100%;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border: 3px solid rgba(255, 255, 255, 0.1);
    }

    .dashboard-demo-title {
      color: white;
      font-size: 28px;
      margin-bottom: 20px;
    }

    .dashboard-demo-description {
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.6;
      margin-bottom: 25px;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin-top: 40px;
    }

    .metric-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-left: 4px solid #F47C20;
    }

    .metric-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(244, 124, 32, 0.2);
    }

    .metric-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #F47C20;
    }

    .metric-description {
      font-size: 14px;
      color: #6E7C8E;
    }

    .key-features-list {
      margin-top: 30px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .key-feature-item {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .key-feature-icon {
      width: 40px;
      height: 40px;
      background-color: rgba(244, 124, 32, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #F47C20;
    }

    .key-feature-text {
      font-weight: 500;
    }

    .benefits-section {
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      padding: 60px 20px;
      margin-top: 60px;
      border-radius: 8px;
    }

    .benefits-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .benefits-title {
      text-align: center;
      margin-bottom: 40px;
      color: #073763;
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }

    .benefit-card {
      background-color: white;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-left: 4px solid #F47C20;
    }

    .benefit-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .benefit-title {
      color: #F47C20;
      margin-bottom: 15px;
      font-size: 1.2rem;
    }

    .benefit-description {
      color: #555;
      line-height: 1.6;
    }

    .back-button {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      color: #073763;
      font-weight: 500;
      margin-bottom: 20px;
      text-decoration: none;
    }

    .back-button:hover {
      text-decoration: underline;
    }

    .dashboard-preview {
      margin-top: 40px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .dashboard-preview:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .dashboard-preview img {
      width: 100%;
      height: auto;
      display: block;
    }

    @media (max-width: 992px) {
      .product-content {
        flex-direction: column;
      }

      .dashboard-demo-content {
        flex-direction: column;
      }

      .key-features-list {
        grid-template-columns: 1fr;
      }

      .metrics-grid,
      .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 576px) {
      .metrics-grid,
      .benefits-grid {
        grid-template-columns: 1fr;
      }

      .benefit-card {
        padding: 20px 15px;
      }
    }
  </style>
</head>
<body>
  <!-- Header Navigation -->
  <header class="header">
    <div class="container header-container">
      <a href="../enhance_agnoshin.html" class="logo-link">
        <img src="../media/AgnoShin New Logo.png" alt="AgnoShin Logo" class="logo">
      </a>

      <nav>
        <ul class="nav-menu">
          <li><a href="../enhance_agnoshin.html" class="nav-link">HOME</a></li>
          <li><a href="../enhance_agnoshin.html#about" class="nav-link">ABOUT US</a></li>
          <li><a href="../enhance_agnoshin.html#services" class="nav-link">SERVICES</a></li>
          <li><a href="../enhance_agnoshin.html#products" class="nav-link active">OUR PRODUCTS</a></li>
          <li><a href="../industries.html" class="nav-link">INDUSTRIES</a></li>
          <li><a href="../enhance_agnoshin.html#partners" class="nav-link">OUR PARTNERS</a></li>
          <li><a href="#" class="nav-link">CSR</a></li>
        </ul>
      </nav>

      <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
      </button>

      <div class="header-actions">
        <a href="../contact-us.html" class="btn btn-primary">CONTACT US</a>
      </div>
    </div>
  </header>

  <!-- Product Detail Section -->
  <section class="section">
    <div class="product-detail-container">
      <a href="../enhance_agnoshin.html#products" class="back-button">
        <i class="fas fa-arrow-left"></i> Back to Products
      </a>

      <div class="product-header">
        <div style="display: flex; align-items: center; gap: 15px;">
          <!-- Keywords for image generation: wall board logo, dashboard display, call center metrics, data visualization, professional -->
          <img src="../media/wallboard_logo.jpeg" alt="Wall Board Logo" style="height: 60px; margin-bottom: 20px; border-radius: 4px;">
          <h1 style="margin-bottom: 20px; color: #F47C20;">Wall Board</h1>
        </div>
        <h2>Real-Time <span style="color: #F47C20;">Performance</span> Visualization</h2>
      </div>

      <div class="product-content">
        <div class="product-description">
          <p>Wall Board is a visual display system used in call centers to monitor and communicate real-time performance metrics to agents and supervisors. These boards are typically large screens or dashboards positioned in common areas of a contact center for easy visibility. They provide a quick, at-a-glance overview of key performance indicators (KPIs) related to customer service operations.</p>

          <p>Our Wall Board solution transforms how contact centers visualize and respond to operational data. With customizable layouts and real-time updates, managers and agents can instantly see critical metrics that drive performance and customer satisfaction.</p>

          <p>The system integrates seamlessly with your existing call center infrastructure, pulling data from multiple sources to create a unified view of operations. Whether you're monitoring service levels, agent performance, or queue status, Wall Board provides the visibility you need to make informed decisions quickly.</p>
        </div>
        <div class="product-image">
          <!-- Keywords for image generation: call center wall board, performance dashboard, digital metrics display, real-time data visualization, professional -->
          <img src="../media/wallboard_main.jpeg" alt="Wall Board Display" style="width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
        </div>
      </div>

      <!-- Features Section -->
      <section class="features-section" style="padding-top: 0;">
        <div class="container">
          <h2 class="section-title"><span style="color: #F47C20;">Key</span> Features</h2>
          <div class="features-grid">
            <!-- Real-Time Updates -->
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-sync-alt"></i>
              </div>
              <h3 class="feature-title">Real-Time Updates</h3>
              <p class="feature-description">Instant data refreshing ensures all metrics are current, with configurable update intervals to match operational needs.</p>
            </div>

            <!-- Customizable Layouts -->
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-th-large"></i>
              </div>
              <h3 class="feature-title">Customizable Layouts</h3>
              <p class="feature-description">Flexible dashboard configuration allows you to design displays that highlight the metrics most relevant to your business.</p>
            </div>

            <!-- Visual Alerts -->
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-bell"></i>
              </div>
              <h3 class="feature-title">Visual Alerts</h3>
              <p class="feature-description">Color-coded thresholds and animated alerts instantly highlight metrics that require attention or intervention.</p>
            </div>

            <!-- Multi-Screen Support -->
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-desktop"></i>
              </div>
              <h3 class="feature-title">Multi-Screen Support</h3>
              <p class="feature-description">Display different metrics on multiple screens throughout your contact center for targeted visibility where it matters most.</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Dashboard Demo Section -->
      <div class="dashboard-demo-section">
        <div class="dashboard-demo-content">
          <div class="dashboard-demo-text">
            <h2 class="dashboard-demo-title">Real-Time Performance Visualization</h2>
            <p class="dashboard-demo-description">Wall Board transforms how contact centers visualize and respond to operational data. With customizable layouts and real-time updates, managers and agents can instantly see critical metrics that drive performance and customer satisfaction.</p>
            <p class="dashboard-demo-description">The system integrates seamlessly with your existing call center infrastructure, pulling data from multiple sources to create a unified view of operations. Whether you're monitoring service levels, agent performance, or queue status, Wall Board provides the visibility you need to make informed decisions quickly.</p>
          </div>
          <div class="dashboard-demo-image">
            <!-- Keywords for image generation: call center dashboard, performance metrics, real-time analytics, data visualization, professional interface -->
            <img src="../media/wallboard_dashboard.jpeg" alt="Wall Board Dashboard Preview">
          </div>
        </div>
      </div>

      <p>For contact centers, Wall Board is an essential tool for maintaining operational excellence and achieving service level targets. By providing a clear, real-time view of performance metrics, it enables supervisors to make data-driven decisions about resource allocation, identify potential issues before they impact service quality, and recognize top-performing agents. The visual nature of the display creates transparency and fosters a culture of accountability and continuous improvement.</p>

      <!-- Key Features Section -->
      <div class="features-container">
        <h2 class="features-title">Key Features</h2>
        <div class="key-features-list">
          <div class="key-feature-item">
            <div class="key-feature-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <div class="key-feature-text">Real-time data updates with configurable refresh intervals</div>
          </div>
          <div class="key-feature-item">
            <div class="key-feature-icon">
              <i class="fas fa-th-large"></i>
            </div>
            <div class="key-feature-text">Customizable layouts and dashboard configurations</div>
          </div>
          <div class="key-feature-item">
            <div class="key-feature-icon">
              <i class="fas fa-bell"></i>
            </div>
            <div class="key-feature-text">Visual alerts and threshold notifications</div>
          </div>
          <div class="key-feature-item">
            <div class="key-feature-icon">
              <i class="fas fa-desktop"></i>
            </div>
            <div class="key-feature-text">Multi-screen support for targeted visibility</div>
          </div>
          <div class="key-feature-item">
            <div class="key-feature-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="key-feature-text">Historical comparison and trend visualization</div>
          </div>
        </div>
      </div>

      <!-- Key Metrics Section -->
      <div style="margin-top: 60px;">
        <h2 class="section-title"><span style="color: #F47C20;">Key</span> Metrics</h2>
        <p style="text-align: center; max-width: 800px; margin: 0 auto 40px;">Our Wall Board solution displays critical performance indicators that help contact centers monitor operations and make data-driven decisions in real-time.</p>

        <div class="metrics-grid">
          <!-- Call Volume -->
          <div class="metric-item">
            <h3 class="metric-title">Call Volume</h3>
            <p class="metric-description">Real-time tracking of incoming and outgoing calls, with historical comparisons to identify patterns and trends.</p>
          </div>

          <!-- Queue Status -->
          <div class="metric-item">
            <h3 class="metric-title">Queue Status</h3>
            <p class="metric-description">Detailed information about waiting times and the number of customers in queue, with color-coded alerts for critical thresholds.</p>
          </div>

          <!-- Service Level -->
          <div class="metric-item">
            <h3 class="metric-title">Service Level</h3>
            <p class="metric-description">Percentage of calls answered within target timeframes (e.g., 80% of calls answered within 20 seconds), with real-time goal tracking.</p>
          </div>

          <!-- Agent Availability -->
          <div class="metric-item">
            <h3 class="metric-title">Agent Availability</h3>
            <p class="metric-description">Current status of all agents (available, on call, in after-call work, on break) with visual indicators for quick assessment.</p>
          </div>

          <!-- Average Handle Time -->
          <div class="metric-item">
            <h3 class="metric-title">Average Handle Time</h3>
            <p class="metric-description">The average duration agents spend on calls, including talk time and after-call work, with comparisons to targets.</p>
          </div>

          <!-- Customer Satisfaction -->
          <div class="metric-item">
            <h3 class="metric-title">Customer Satisfaction</h3>
            <p class="metric-description">Real-time CSAT scores from post-call surveys, with trend indicators and alerts for significant changes.</p>
          </div>

          <!-- First Call Resolution -->
          <div class="metric-item">
            <h3 class="metric-title">First Call Resolution</h3>
            <p class="metric-description">Percentage of customer issues resolved on the first contact, reducing the need for follow-up calls and improving satisfaction.</p>
          </div>

          <!-- Wait Time -->
          <div class="metric-item">
            <h3 class="metric-title">Wait Time</h3>
            <p class="metric-description">Current and average wait times for customers in queue, with threshold alerts to prevent excessive waiting.</p>
          </div>

          <!-- Agent Performance -->
          <div class="metric-item">
            <h3 class="metric-title">Agent Performance</h3>
            <p class="metric-description">Individual and team performance metrics, including calls handled, resolution rates, and quality scores.</p>
          </div>
        </div>
      </div>

      <!-- Integration Section -->
      <div style="background-color: #f5f5f5; padding: 40px 20px; border-radius: 10px; margin-top: 60px;">
        <h2 class="section-title" style="text-align: center;">Seamless <span style="color: #F47C20;">Integration</span></h2>
        <p style="text-align: center; max-width: 800px; margin: 0 auto 40px;">Our Wall Board solution integrates with your existing contact center infrastructure to provide a unified view of performance.</p>

        <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 30px; max-width: 1000px; margin: 0 auto;">
          <!-- ACD Integration -->
          <div style="flex: 0 0 calc(33.33% - 30px); background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); border-bottom: 3px solid #F47C20;">
            <div style="text-align: center; margin-bottom: 15px;">
              <i class="fas fa-phone-volume" style="font-size: 36px; color: #F47C20;"></i>
            </div>
            <h3 style="text-align: center; color: #F47C20; margin-bottom: 10px;">ACD Systems</h3>
            <p style="text-align: center; color: #6E7C8E;">Connects with all major Automatic Call Distribution systems to display real-time call routing and queue information.</p>
          </div>

          <!-- CRM Integration -->
          <div style="flex: 0 0 calc(33.33% - 30px); background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); border-bottom: 3px solid #F47C20;">
            <div style="text-align: center; margin-bottom: 15px;">
              <i class="fas fa-users" style="font-size: 36px; color: #F47C20;"></i>
            </div>
            <h3 style="text-align: center; color: #F47C20; margin-bottom: 10px;">CRM Platforms</h3>
            <p style="text-align: center; color: #6E7C8E;">Integrates with customer relationship management systems to display customer satisfaction metrics and case resolution data.</p>
          </div>

          <!-- AgnoShin Products -->
          <div style="flex: 0 0 calc(33.33% - 30px); background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); border-bottom: 3px solid #F47C20;">
            <div style="text-align: center; margin-bottom: 15px;">
              <i class="fas fa-puzzle-piece" style="font-size: 36px; color: #F47C20;"></i>
            </div>
            <h3 style="text-align: center; color: #F47C20; margin-bottom: 10px;">AgnoShin Ecosystem</h3>
            <p style="text-align: center; color: #6E7C8E;">Seamlessly works with AgnoCon, Outbound Dialer, and other AgnoShin products for a comprehensive contact center solution.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Use Cases Section -->
  <section class="benefits-section" style="background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);">
    <div class="benefits-container">
      <h2 class="benefits-title">Common <span style="color: #F47C20;">Use Cases</span></h2>
      <div class="benefits-grid">
        <div class="benefit-card" style="border-left: 4px solid #F47C20;">
          <h3 class="benefit-title">Contact Center Management</h3>
          <p class="benefit-description">Monitor real-time performance metrics across multiple teams and locations, enabling supervisors to make immediate adjustments to staffing and call routing to maintain service levels.</p>
        </div>

        <div class="benefit-card" style="border-left: 4px solid #F47C20;">
          <h3 class="benefit-title">Agent Performance Tracking</h3>
          <p class="benefit-description">Display individual and team performance metrics to foster healthy competition, recognize achievements, and identify agents who may need additional coaching or support.</p>
        </div>

        <div class="benefit-card" style="border-left: 4px solid #F47C20;">
          <h3 class="benefit-title">Executive Dashboards</h3>
          <p class="benefit-description">Provide high-level operational insights to management and executives, highlighting key performance indicators and business metrics in an easily digestible format.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="benefits-section">
    <div class="benefits-container">
      <h2 class="benefits-title">Business <span style="color: #F47C20;">Benefits</span></h2>
      <div class="benefits-grid">
        <div class="benefit-card">
          <h3 class="benefit-title">Real-Time Performance Monitoring</h3>
          <p class="benefit-description">Instant visibility into contact center operations allows managers to identify and address issues before they impact customer experience, reducing the time to respond to changing conditions.</p>
        </div>

        <div class="benefit-card">
          <h3 class="benefit-title">Increased Accountability</h3>
          <p class="benefit-description">Transparent performance metrics foster a culture of accountability and continuous improvement among agents and supervisors, leading to higher standards of service delivery.</p>
        </div>

        <div class="benefit-card">
          <h3 class="benefit-title">Improved Team Collaboration</h3>
          <p class="benefit-description">Shared visibility of goals and performance encourages teamwork and collective problem-solving across the contact center, breaking down silos between departments.</p>
        </div>

        <div class="benefit-card">
          <h3 class="benefit-title">Enhanced Operational Efficiency</h3>
          <p class="benefit-description">Data-driven insights enable optimized staffing, improved call routing, and streamlined processes that reduce costs while maintaining or improving service quality.</p>
        </div>

        <div class="benefit-card">
          <h3 class="benefit-title">Performance Motivation</h3>
          <p class="benefit-description">Visual representation of goals and achievements motivates agents to improve their performance and reach targets, creating a positive competitive environment.</p>
        </div>

        <div class="benefit-card">
          <h3 class="benefit-title">Improved Customer Experience</h3>
          <p class="benefit-description">Better monitoring leads to faster response times, shorter wait periods, and more effective issue resolution for customers, resulting in higher satisfaction scores.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container footer-container">
      <div class="footer-content">
        <div>
          <img src="https://agnoshin.com/wp-content/uploads/2023/08/agnoshin-logo.png" alt="AgnoShin Logo" class="footer-logo">
          <p class="footer-description">AgnoShin is a technology-agnostic company with proven in-depth expertise across verticals and technologies.</p>
        </div>

        <div>
          <h3 class="footer-title">Quick Links</h3>
          <ul class="footer-links">
            <li class="footer-link"><a href="../enhance_agnoshin.html">Home</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#about">About Us</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#services">Services</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#products">Products</a></li>
            <li class="footer-link"><a href="../enhance_agnoshin.html#partners">Partners</a></li>
          </ul>
        </div>

        <div>
          <h3 class="footer-title">Contact Us</h3>
          <ul class="footer-contact">
            <li class="footer-contact-item"><i class="fas fa-map-marker-alt"></i> 123 Business Avenue, Tech City</li>
            <li class="footer-contact-item"><i class="fas fa-phone"></i> +****************</li>
            <li class="footer-contact-item"><i class="fas fa-envelope"></i> <EMAIL></li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="copyright">
          © 2025 AgnoShin Technologies. All rights reserved.
        </div>

        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="../advanced_interactions.js"></script>
</body>
</html>

