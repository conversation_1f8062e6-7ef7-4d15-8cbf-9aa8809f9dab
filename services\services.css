/* Services Pages Common Styles */

/* Base styles */
body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero section */
.hero-section {
  background-color: #0B2A5A;
  color: white;
  padding: 60px 0;
  text-align: center;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
  color: white;
}

.hero-subtitle {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
  font-weight: 400;
  color: white;
}

/* Content sections */
.content-section {
  padding: 60px 0;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  color: #0B2A5A;
  font-size: 2rem;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 700;
}

/* Feature grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: #F47C20;
  margin-bottom: 20px;
}

.feature-title {
  font-size: 1.5rem;
  color: #0B2A5A;
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-description {
  color: #555;
  line-height: 1.6;
}

/* Content rows */
.content-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 60px 0;
}

.content-col {
  flex: 1;
  min-width: 300px;
  padding: 20px;
}

.content-image {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.content-text h2 {
  color: #0B2A5A;
  margin-bottom: 20px;
  font-weight: 600;
}

.content-text p {
  color: #555;
  line-height: 1.8;
  margin-bottom: 15px;
}

.content-text ul {
  color: #555;
  line-height: 1.8;
  margin-bottom: 15px;
  padding-left: 20px;
}

.content-text li {
  margin-bottom: 10px;
}

/* CTA section */
.cta-section {
  background-color: #f8f9fa;
  padding: 60px 0;
  text-align: center;
}

.cta-title {
  font-size: 2rem;
  color: #0B2A5A;
  margin-bottom: 20px;
  font-weight: 700;
}

.cta-subtitle {
  font-size: 1.1rem;
  color: #555;
  max-width: 700px;
  margin: 0 auto 30px;
}

.btn-primary {
  background-color: #F47C20;
  color: white;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: #e06a10;
}

/* Navigation */
.product-nav {
  background-color: #f5f5f5;
  padding: 15px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  position: relative;
}

.back-link-container {
  width: 150px;
}

.spacer {
  width: 150px;
}

.back-link {
  color: #0B2A5A;
  text-decoration: none;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
}

.back-link i {
  margin-right: 5px;
}

.product-title {
  font-size: 1.5rem;
  color: #0B2A5A;
  font-weight: 600;
  text-align: center;
  font-family: 'Inter', sans-serif;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin: 0;
}

.demo-button {
  background-color: #F47C20;
  color: white;
  padding: 8px 20px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.demo-button:hover {
  background-color: #e06a10;
}

/* Footer */
.footer {
  background-color: #001a4d;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40px;
}

.footer-content > div {
  flex: 1;
  min-width: 250px;
  margin-bottom: 30px;
}

.footer-title {
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link {
  margin-bottom: 10px;
}

.footer-link a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link a:hover {
  color: #F47C20;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
  font-size: 0.9rem;
  color: #ddd;
}

.social-links {
  display: flex;
}

.social-link {
  color: white;
  margin-left: 15px;
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: #F47C20;
}

.footer-link i {
  color: white;
  margin-right: 8px;
}

/* Customer Experience Specific Styles */
.cx-approach-item {
  margin-bottom: 30px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #F47C20;
}

.cx-approach-item h3 {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cx-approach-item h3 i {
  font-size: 1.2rem;
}

/* Security Services Specific Styles */
.lifecycle-stage {
  margin-bottom: 30px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #F47C20;
}

.lifecycle-stage h3 {
  display: flex;
  align-items: center;
  gap: 10px;
}

.lifecycle-stage h3 i {
  font-size: 1.2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .content-row {
    flex-direction: column;
  }

  .nav-container {
    padding: 10px 15px;
  }

  .back-link-container, .spacer {
    width: 100px;
  }

  .product-title {
    font-size: 1.2rem;
    width: 100%;
  }

  .footer-content {
    flex-direction: column;
  }

  .footer-bottom {
    flex-direction: column;
  }

  .social-links {
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .back-link-container {
    width: 80px;
  }

  .spacer {
    display: none;
  }

  .product-title {
    font-size: 1rem;
  }
}
